-- Cosmic系统配置示例
-- 在t_expense_config表中插入配置数据

-- 默认配置
INSERT INTO `t_expense_config` (
    `id`, 
    `expense_project_code`, 
    `config_type`, 
    `config_name`, 
    `config_content`, 
    `create_date`, 
    `remark`
) VALUES (
    UUID(), 
    'DEFAULT', 
    'cosmic', 
    '轨道金蝶系统默认配置', 
    '{
        "url": "http://edstest.xmgdjt.com.cn:9000/cosmic/{}?appKey=Fy2jZ7xKuc58",
        "clientId": "DMK",
        "clientSecret": "Qffhrweio123@fhdsjbn",
        "username": "duomikegongyuxitong",
        "accountId": "1951600800203540480"
    }', 
    NOW(), 
    'Cosmic系统默认配置'
);

-- 项目特定配置示例（如果需要不同项目使用不同配置）
INSERT INTO `t_expense_config` (
    `id`, 
    `expense_project_code`, 
    `config_type`, 
    `config_name`, 
    `config_content`, 
    `create_date`, 
    `remark`
) VALUES (
    UUID(), 
    'PROJECT_A', 
    'cosmic', 
    '项目A的Cosmic配置', 
    '{
        "url": "http://edstest.xmgdjt.com.cn:9000/cosmic/{}?appKey=Fy2jZ7xKuc58",
        "clientId": "PROJECT_A_CLIENT",
        "clientSecret": "PROJECT_A_SECRET",
        "username": "projecta_user",
        "accountId": "1951600800203540480"
    }', 
    NOW(), 
    '项目A专用的Cosmic配置'
);

-- 生产环境配置示例
INSERT INTO `t_expense_config` (
    `id`, 
    `expense_project_code`, 
    `config_type`, 
    `config_name`, 
    `config_content`, 
    `create_date`, 
    `remark`
) VALUES (
    UUID(), 
    'PRODUCTION', 
    'cosmic', 
    'Cosmic生产环境配置', 
    '{
        "url": "http://prod.xmgdjt.com.cn:9000/cosmic/{}?appKey=ProdAppKey123",
        "clientId": "PROD_DMK",
        "clientSecret": "ProdSecretKey@2024",
        "username": "prod_cosmic_user",
        "accountId": "1951600800203540480"
    }', 
    NOW(), 
    'Cosmic系统生产环境配置'
);

/*
配置说明：
1. expense_project_code: 配置编码，用于标识不同的配置
   - DEFAULT: 默认配置
   - PROJECT_A: 项目A专用配置
   - PRODUCTION: 生产环境配置

2. config_type: 固定为'cosmic'，对应ApiTypeEnum.COSMIC

3. config_content: JSON格式的配置内容，包含：
   - url: Cosmic系统API基础URL
   - clientId: 第三方应用系统编码（appId）
   - clientSecret: 第三方应用AccessToken认证密钥（appSecret）
   - username: 第三方应用代理用户的用户名
   - accountId: 数据中心id

使用方式：
- 在代码中调用 cosmicBaseApi.getToken("DEFAULT") 获取默认配置的token
- 在代码中调用 cosmicBaseApi.getToken("PROJECT_A") 获取项目A配置的token
- 在代码中调用 cosmicBaseApi.settleTypeQuery("PRODUCTION") 使用生产环境配置查询结算方式
*/ 