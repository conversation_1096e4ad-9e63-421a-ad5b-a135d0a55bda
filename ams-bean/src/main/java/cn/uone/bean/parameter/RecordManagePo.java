package cn.uone.bean.parameter;

import cn.uone.web.util.UoneHeaderUtil;
import lombok.Data;

@Data
public class RecordManagePo {

    /***
     * 项目ID
     */
    private String projectId = UoneHeaderUtil.getProjectId();

    /**
     * 分区ID
     */
    private String partitionId;

    /**
     * 关键字
     */
    private String keyWord;

    private String isOrganize;

    private String recordState;

    private String contractState;


    private String minCheckOutDate;

    private String maxCheckOutDate;


    private String minContractStartDate;

    private String maxContractStartDate;

    private String minContractEndDate;

    private String maxContractEndDate;

    /**
     * 签约人
     */
    private String signer;

    private String csId;
}
