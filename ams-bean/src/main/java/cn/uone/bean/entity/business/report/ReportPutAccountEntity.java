package cn.uone.bean.entity.business.report;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 收费台账
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_report_put_account")
public class ReportPutAccountEntity extends BaseModel<ReportPutAccountEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 账单ID
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 合同编号
     */
    @TableField("contract_id")
    private String contractId;

    /**
     * 房源ID
     */
    @TableField("source_id")
    private String sourceId;


    /**
     * 合同编号
     */
    @TableField("cont_code")
    private String contCode;


    /**
     * 账单编号
     */
    @TableField("order_code")
    private String orderCode;

    /**
     * 签约人姓名
     */
    @TableField("signer_name")
    private String signerName;

    /**
     * 合同状态
     */
    @TableField("cont_state")
    private String contState;

    /**
     * 月租金
     */
    @TableField("rent")
    private BigDecimal rent;

    /**
     * 补贴金额
     */
    @TableField("cont_subsidy")
    private BigDecimal contSubsidy = BigDecimal.ZERO;

    /**
     * 合同租金
     */
    @TableField("real_rent")
    private BigDecimal realRent;

    /**
     * 租赁起始日
     */
    @TableField("cont_start_date")
    private LocalDateTime contStartDate;

    /**
     * 租赁截止日
     */
    @TableField("cont_end_date")
    private LocalDateTime contEndDate;

    /**
     * 缴费方式
     */
    @TableField("pay_type")
    private String payType;

    /**
     * 应收金额
     */
    @TableField("order_must_rent")
    private BigDecimal orderMustRent= BigDecimal.ZERO;

    /**
     * 实收金额
     */
    @TableField("order_real_rent")
    private BigDecimal orderRealRent = BigDecimal.ZERO;

    /**
     * 账单补贴金额
     */
    @TableField("order_subsidy")
    private BigDecimal orderSubsidy = BigDecimal.ZERO;

    /**
     * 账单优惠金额
     */
    @TableField("order_discount")
    private BigDecimal orderDiscount = BigDecimal.ZERO;

    /**
     * 账单起始日
     */
    @TableField("order_start_date")
    private Date orderStartDate;

    /**
     * 账单截止日
     */
    @TableField("order_end_date")
    private Date orderEndDate;

    /**
     * 本月应收金额
     */
    @TableField("this_mon_rent")
    private BigDecimal thisMonRent = BigDecimal.ZERO;

    /**
     * 本月补贴金额
     */
    @TableField("this_mon_subsidy")
    private BigDecimal thisMonSubsidy = BigDecimal.ZERO;

    /**
     * 本月优惠金额
     */
    @TableField("this_mon_discount")
    private BigDecimal thisMonDiscount = BigDecimal.ZERO;

    /**
     * 本月实收金额
     */
    @TableField("this_mon_real_rent")
    private BigDecimal thisMonRealRent = BigDecimal.ZERO;

    /**
     * 本月预收金额
     */
    @TableField("this_mon_ahead")
    private BigDecimal thisMonAhead = BigDecimal.ZERO;

    /**
     * 应退租金
     */
    @TableField("checkout_rent")
    private BigDecimal checkoutRent = BigDecimal.ZERO;

    /**
     * 应退租金起始日
     */
    @TableField("checkout_start_date")
    private Date checkoutStartDate;

    /**
     * 应退租金截止日
     */
    @TableField("checkout_end_date")
    private Date checkoutEndDate;

    /**
     * 实际退租日期
     */
    @TableField("checkout_date")
    private Date checkoutDate;

    /**
     * 应退金额所属缴款日期
     */
    @TableField("checkout_confirm_date")
    private Date checkoutConfirmDate;

    /**
     * 发票号
     */
    @TableField("invoice_code")
    private String invoiceCode;

    /**
     * 缴费日期
     */
    @TableField("pay_date")
    private Date payDate;

    /**
     * 缴费日期
     */
    @TableField("report_date")
    private Date reportDate;




    /**
     * 逾期情况
     */
    @TableField("over_state")
    private String overState;

    @TableField(exist = false)
    private String contractSourceId;

    /**
     * 账单类型
     */
    @TableField(exist = false)
    private String orderType;

    /***
     * 支付时间跨度
     */
    @TableField(exist = false)
    private Integer span=0;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
