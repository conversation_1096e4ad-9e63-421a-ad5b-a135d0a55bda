package cn.uone.bean.entity.business.kingdee;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_kingdee_customer")
public class KingdeeCustomerEntity extends BaseModel<KingdeeCustomerEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 客户段编号
     */
    @TableField("code")
    private String code;

    /**
     * 客户段名称
     */
    @TableField("name")
    private String name;

    /**
     * 子帐单类型
     */
    @TableField("order_item_type")
    private String orderItemType;

    /**
     * 项目id
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 分区id
     */
    @TableField("partition_id")
    private String partitionId;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
