package cn.uone.bean.entity.tpi.icbcPay;

import lombok.Data;

@Data
public class IcbcPayConfigVo {
    /**
     * 商户编号
     */
    private String merId;
    /**
     * 收单产品协议编号
     */
    private String merPrtclNo;
    /**
     * 异步通知商户URL，端口必须为443或80
     */
    private String merUrl;
    /**
     * 商户在微信开放平台注册的APPID，支付方式为微信时不能为空
     */
    private String shopAppid;
    /**
     * 商户在工行API平台的APPID
     */
    private String icbcAppid;
    /**
     * 商户账号，商户入账账号
     */
    private String merAcct;

    /**
     * 网关密钥
     */
    private String apigwPublicKey;
    /**
     * 私钥
     */
    private String myPrivateKey;

    /**
     * AES密钥
     */
    private String aesKey;

}
