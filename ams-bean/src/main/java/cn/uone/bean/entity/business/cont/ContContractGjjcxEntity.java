package cn.uone.bean.entity.business.cont;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 公积金查询表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cont_contract_gjjcx")
public class ContContractGjjcxEntity extends BaseModel<ContContractGjjcxEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 合同id
     */
    @TableField("contract_id")
    private String contractId;

    /**
     * 证件号码
     */
    @TableField("zjhm")
    private String zjhm;

    /**
     * 姓名
     */
    @TableField("xingming")
    private String xingming;

    /**
     * 交易日期
     */
    @TableField("trs_date")
    private String trsDate;

    /**
     * 交易时间
     */
    @TableField("trs_time")
    private String trsTime;

    /**
     * 账户开户银行名称
     */
    @TableField("zhkhyhmc")
    private String zhkhyhmc;

    /**
     * 单位名称
     */
    @TableField("dwmc")
    private String dwmc;

    /**
     * 个人账号
     */
    @TableField("grzh")
    private String grzh;

    /**
     * 个人账户余额
     */
    @TableField("grzhye")
    private String grzhye;

    /**
     * 个人账户状态
     */
    @TableField("grzhzt")
    private String grzhzt;

    /**
     * 本月租房可支取金额
     */
    @TableField("byzfkzqje")
    private BigDecimal byzfkzqje;

    /**
     * 本月租房已支取金额
     */
    @TableField("byzfyzfje")
    private BigDecimal byzfyzfje;

    /**
     * 提取年月
     */
    @TableField("tqny")
    private String tqny;

    /**
     * 提取类型
     */
    @TableField("tqlx")
    private String tqlx;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
