package cn.uone.bean.entity.crm;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_role")
public class RoleEntity extends BaseModel<RoleEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 角色名称
     */
    @TableField("name")
    private String name;

    /**
     * 角色级别
     */
    @TableField("grade")
    private String grade;

    @TableField("remarks")
    private String remarks;

    @TableLogic
    @TableField("del_flag")
    private Boolean delFlag;

    @TableField("company_id")
    private String companyId;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
