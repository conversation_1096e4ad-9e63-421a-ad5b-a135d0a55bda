package cn.uone.bean.entity.business.xhcosmic;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import cn.uone.web.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 金蝶(星瀚)系统财务应付单 计量工程单据体
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cosmic_accounts_payable_engine")
public class CosmicAccountsPayableEngineEntity extends BaseModel<CosmicAccountsPayableEngineEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("accounts_payable_id")
    private String accountsPayableId;

    /**
     * 工程名称
     */
    @TableField("engineering_number")
    private String engineeringNumber;

    /**
     * 计量工程单据体.本月完成金额
     */
    @TableField("month_amount")
    private BigDecimal monthAmount;


    @Override
    protected Serializable pkVal() {
        return id;
    }

}
