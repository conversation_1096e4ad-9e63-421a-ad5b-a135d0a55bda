package cn.uone.bean.entity.business.invoice.vo;

import lombok.Data;


/**
 * <p>
 * 开票购方信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-02
 */
@Data
public class InvoiceBuyerVo {

    /**
     * 账单id
     */
    private String orderId;

    /**
     * 发票种类编码(默认026):01-数电票(增值税专用发票),02-数电票(普通发票)
     */
    private String invoiceTypeCode;

    /**
     * 购方税号
     */
    private String buyerTaxNo;

    /**
     * 购方名称
     */
    private String buyerName;

    /**
     * 购方地址
     */
    private String buyerAddress;

    /**
     * 购方固定电话
     */
    private String buyerTelephone;

    /**
     * 购方开户行
     */
    private String buyerBankName;

    /**
     * 购方银行账号
     */
    private String buyerBankNumber;

}
