package cn.uone.bean.entity.crm;

import com.baomidou.mybatisplus.annotation.TableField;
import com.google.common.collect.Lists;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 项目信息
 */
@Data
public class ProjectEntity {

    private String cityCode;

    private String city;

    private String id;

    /**
     * 项目名
     */
    private String name;

    private String value;

    public String getValue() {
        return id;
    }

    /**
     * 建筑面积
     */
    private BigDecimal area;

    /**
     * 预计房间数（间）
     */
    private Integer roomNum;

    /**
     * 省
     */
    private String provinceId;

    /**
     * 市
     */
    private String cityId;

    /**
     * 县
     */
    private String districtId;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 项目简介
     */
    private String summary;

    /**
     * 拓展状态
     */
    private String expandState;

    /**
     * 运营状态
     */
    private String operateState;

    /**
     * 分区数据列表
     */
    private List<ZoneEntity> zones = Lists.newArrayList();


    /**
     * 项目缩略图地址
     */
    @TableField(exist = false)
    private String imageUrl;

}
