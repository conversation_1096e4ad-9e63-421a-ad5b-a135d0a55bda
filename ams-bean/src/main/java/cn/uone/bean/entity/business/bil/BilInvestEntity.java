package cn.uone.bean.entity.business.bil;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_bil_invest")
public class BilInvestEntity extends BaseModel<BilInvestEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableField("code")
    private String code;

    /**
     * 房源id
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 合同id
     */
    @TableField("contract_id")
    private String contractId;

    /**
     * 支付金额
     */
    @TableField("payable_payment")
    private BigDecimal payablePayment;

    /**
     * 交易回调码
     */
    @TableField("payment")
    private BigDecimal payment;

    @TableField("trade_code")
    private String tradeCode;

    /**
     * 支付方式
     */
    @TableField("pay_way")
    private String payWay;

    /**
     * 账号id
     */
    @TableField("account_id")
    private String accountId;

    /**
     * 支付状态，见【PayStateEnum】
     */
    @TableField("pay_state")
    private String payState;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    private Date payTime;

    /**
     * 支付时间
     */
    @TableField(exist = false)
    private String payTimeStr;

    /**
     * 支付时间
     */
    @TableField(exist = false)
    private String partitionId;

    /**
     * 支付时间
     */
    @TableField(exist = false)
    private String projectId;

    /**
     * 支付时间
     */
    @TableField(exist = false)
    private String projectName;



    /**
     * 账单类型，见【OrderTypeEnum】
     */
    @TableField("order_type")
    private String orderType;

    /**
     * 支付人id
     */
    @TableField("payer_id")
    private String payerId;

    /**
     * 数据来源
     */
    @TableField("data_from")
    private String dataFrom;

    /**
     * 开票类型，见【InvoiceTypeEnum】
     */
    @TableField("invoice_type")
    private String invoiceType;

    /**
     * 开票状态（见【InvoiceStateEnum】
     */
    @TableField("invoice_state")
    private String invoiceState;

    /**
     * 审批状态，【ApprovalStateEnum】
     */
    @TableField("approval_state")
    private String approvalState;

    /**
     * 是否推送 1：已推送 0：未推送
     */
    @TableField("is_push")
    private String isPush;

    /**
     * 推送时间
     */
    @TableField("push_time")
    private Date pushTime;

    /**
     * 清算时间
     */
    @TableField("clean_time")
    private Date cleanTime;

    /**
     * 取消时间
     */
    @TableField("cancel_time")
    private Date cancelTime;

    /**
     * 是否移入回收站1：已移入回收站
     */
    @TableField("is_discard")
    private String isDiscard;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 转账状态（0未转账1已转账）
     */
    @TableField("transfer_state")
    private String transferState;

    /**
     *  申请开票时间
     */
    @TableField("apply_inv_time")
    private Date applyInvTime;

    @TableField("transfer_time")
    private Date transferTime;

    @TableField("transfer_type")
    private String transferType;

    /**
     * 到账时间
     */
    @TableField("arrive_time")
    private Date arriveTime;

    /**
     * 对账编码
     */
    @TableField("arrive_code")
    private String arriveCode;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
