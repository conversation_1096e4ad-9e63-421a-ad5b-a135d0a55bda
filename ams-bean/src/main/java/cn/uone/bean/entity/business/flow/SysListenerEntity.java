package cn.uone.bean.entity.business.flow;

import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 流程监听
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_listener")
public class SysListenerEntity extends BaseModel<SysListenerEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 监听类型
     */
    @TableField("type")
    private String type;

    /**
     * 事件类型
     */
    @TableField("event_type")
    private String eventType;

    /**
     * 值类型
     */
    @TableField("value_type")
    private String valueType;

    /**
     * 执行内容
     */
    @TableField("value")
    private String value;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
