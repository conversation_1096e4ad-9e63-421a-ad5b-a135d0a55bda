package cn.uone.bean.entity.crm;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 开户项目接口配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_expense_config")
public class ExpenseConfigEntity extends BaseModel<ExpenseConfigEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 开户项目id
     */
    @TableField("expense_project_id")
    private String expenseProjectId;

    /**
     * 开户项目编码
     */
    @TableField("expense_project_code")
    private String expenseProjectCode;

    /**
     * 回调识别id（接口参数）
     */
    @TableField("notify_id")
    private String notifyId;

    /**
     * 配置类型
     */
    @TableField("config_type")
    private String configType;

    /**
     * 配置名称
     */
    @TableField("config_name")
    private String configName;

    /**
     * 配置内容 json
     */
    @TableField("config_content")
    private String configContent;

    @TableField("remark")
    private String remark;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
