package cn.uone.bean.entity.business.demo;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_res_project_owner")
public class DemoOwnerEntity extends BaseModel<DemoOwnerEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    @TableField("name")
    private String ownerName;

    /**
     * 证件类型
     */
    @TableField("id_type")
    private String idType;

    /**
     * 证件号码
     */
    @TableField("id_no")
    private String idNo;

    /**
     * 省
     */
    @TableField("province_id")
    private String provinceId;

    /**
     * 市
     */
    @TableField("city_id")
    private String cityId;

    /**
     * 县
     */
    @TableField("district_id")
    private String districtId;

    /**
     * 详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 邮编
     */
    @TableField("postcode")
    private String postcode;

    /**
     * 联系人
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 电话
     */
    @TableField("tel")
    private String tel;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    @TableField("bank_account")
    private String bankAccount;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
