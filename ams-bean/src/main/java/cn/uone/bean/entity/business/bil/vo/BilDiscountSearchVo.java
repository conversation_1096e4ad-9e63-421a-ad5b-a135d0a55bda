package cn.uone.bean.entity.business.bil.vo;

import cn.uone.bean.entity.business.bil.BilDiscountEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by xmlin on 2018-12-15.
 */
@Data
public class BilDiscountSearchVo extends BilDiscountEntity implements Serializable {
    /**
     * 子劵id
     */
    private String logId;
    /**
     * 子劵码
     */
    private String itemCode;
    /**
     * 账单id
     */
    private String orderId;
    /**
     * 账单编号
     */
    private String orderCode;
    /**
     * 合同id
     */
    private String contractId;
    /**
     * 合同编号
     */
    private String contractCode;
    /**
     * 房源地址
     */
    private String sourceName;
    /**
     * 申请人
     */
    private String realName;
    /**
     * 签约方
     */
    private String signName;
    /**
     * 使用人
     */
    private String payerName;
    /**
     * 手机号码
     */
    private String payerTel;
    /**
     * 使用人
     */
    private String state;
    /**
     * 支付时间
     */
    private Date payTime;

    private List<String> ids;

}
