package cn.uone.bean.entity.business.dev;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 设备读数日志
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_dev_device_log")
public class DevDeviceLogEntity extends BaseModel<DevDeviceLogEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("device_id")
    private String deviceId;

    /**
     * 读数
     */
    @TableField("read_num")
    private BigDecimal readNum;

    /**
     * 校准数
     */
    @TableField("check_num")
    private BigDecimal checkNum;

    /**
     * 抄表时间
     */
    @TableField("read_time")
    private Date readTime;

    /**
     * 数据来源
     */
    @TableField("data_from")
    private String dataFrom;

    /**
     * 度数设备类型：1水，2电，3煤气
     */
    @TableField("type")
    private String type;

    /**
     * 设备状态
     */
    @TableField("device_state")
    private String deviceState;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
