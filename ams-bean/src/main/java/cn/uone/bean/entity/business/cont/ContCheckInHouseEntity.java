package cn.uone.bean.entity.business.cont;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_cont_check_in_house")
public class ContCheckInHouseEntity extends BaseModel<ContCheckInHouseEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 合同房源关系id
     */
    @TableField("contract_source_id")
    private String contractSourceId;

    /**
     * 租客用户id
     */
    @TableField("renter_id")
    private String renterId;

    /**
     * 是否已办理入住  0未办理入住   1办理入住    2已经搬离
     */
    @TableField("examine")
    private String examine;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @Override
    public Serializable pkVal() {
        return id;
    }

}
