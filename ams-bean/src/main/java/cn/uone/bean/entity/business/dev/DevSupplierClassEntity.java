package cn.uone.bean.entity.business.dev;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_dev_supplier_class")
public class DevSupplierClassEntity extends BaseModel<DevSupplierClassEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("parent_id")
    private String parentId;

    /**
     * 类型名称
     */
    @TableField("name")
    private String name;

    /**
     * 描述
     */
    @TableField("summary")
    private String summary;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
