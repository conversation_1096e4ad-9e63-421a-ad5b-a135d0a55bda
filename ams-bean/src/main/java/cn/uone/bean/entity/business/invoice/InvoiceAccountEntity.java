package cn.uone.bean.entity.business.invoice;

import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_invoice_account")
public class InvoiceAccountEntity extends BaseModel<InvoiceAccountEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("project_id")
    private String projectId;

    /**
     * 开票终端/数电账号
     */
    @TableField("invoice_terminal_code")
    private String invoiceTerminalCode;

    /**
     * 销方税号
     */
    @TableField("invoice_tax_no")
    private String invoiceTaxNo;

    /**
     * 销方名称
     */
    @TableField("invoice_seller_name")
    private String invoiceSellerName;

    /**
     * 销方地址
     */
    @TableField("invoice_seller_address")
    private String invoiceSellerAddress;

    /**
     * 销方电话
     */
    @TableField("invoice_seller_phone")
    private String invoiceSellerPhone;

    /**
     * 销方开户行
     */
    @TableField("invoice_seller_bank_name")
    private String invoiceSellerBankName;

    /**
     * 销方银行账户
     */
    @TableField("invoice_seller_bank_number")
    private String invoiceSellerBankNumber;

    /**
     * 开票员
     */
    @TableField("invoice_drawer")
    private String invoiceDrawer;

    /**
     * 收款人
     */
    @TableField("invoice_payee")
    private String invoicePayee;

    /**
     * 收款人
     */
    @TableField("invoice_checker")
    private String invoiceChecker;

    /**
     * 状态
     */
    @TableField("state")
    private String state;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
