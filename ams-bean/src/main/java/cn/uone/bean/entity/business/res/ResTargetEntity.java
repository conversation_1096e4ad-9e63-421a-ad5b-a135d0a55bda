package cn.uone.bean.entity.business.res;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_res_target")
public class ResTargetEntity extends BaseModel<ResTargetEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("project_id")
    private String projectId;

    /**
     * 年份
     */
    @TableField("year")
    private int year;

    /**
     * 年度目标（金额）
     */
    @TableField("target_price")
    private BigDecimal targetPrice;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
