package cn.uone.bean.entity.crm;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_qiyuesuo_auth")
public class QiyuesuoAuthEntity extends BaseModel<QiyuesuoAuthEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @TableField("expense_project_id")
    private String expenseProjectId;

    /**
     * 联系人
     */
    @TableField("qys_contact_name")
    private String qysContactName;

    /**
     * 联系方式
     */
    @TableField("qys_contact")
    private String qysContact;

    /**
     * 联系类型：MOBILE(手机号),EMAIL(邮箱)
     */
    @TableField("qys_contact_type")
    private String qysContactType;

    /**
     * 契约锁认证 UNREGISTERED:未注册 CERTIFYING:认证中 AUTH_SUCCESS:认证成功
     */
    @TableField("qys_auth_state")
    private String qysAuthState;

    /**
     * 契约锁企业公司ID
     */
    @TableField("qys_company_id")
    private String qysCompanyId;

    /**
     * 契约锁授权token
     */
    @TableField("qys_access_token")
    private String qysAccessToken;

    /**
     * 契约锁授权密钥
     */
    @TableField("qys_access_secret")
    private String qysAccessSecret;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 独立项目的接口地址
     */
    @TableField("url")
    private String url;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
