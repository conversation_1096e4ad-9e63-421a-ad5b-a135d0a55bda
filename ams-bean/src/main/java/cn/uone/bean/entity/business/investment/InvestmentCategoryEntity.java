package cn.uone.bean.entity.business.investment;

import com.baomidou.mybatisplus.annotation.TableName;
import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 品类表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_investment_category")
public class InvestmentCategoryEntity extends BaseModel<InvestmentCategoryEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 品类
     */
    @TableField("category")
    private String category;

    /**
     * 业态
     */
    @TableField("business_name")
    private String businessName;

    /**
     * 业态id
     */
    @TableField("business_id")
    private String businessId;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;



    @Override
    public Serializable pkVal() {
        return id;
    }

}
