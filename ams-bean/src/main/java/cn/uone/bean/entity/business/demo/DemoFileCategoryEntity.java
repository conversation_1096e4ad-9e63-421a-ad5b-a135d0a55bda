package cn.uone.bean.entity.business.demo;

import cn.uone.bean.entity.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("demo_file_category")
public class DemoFileCategoryEntity extends BaseModel<DemoFileCategoryEntity> {

    private static final long serialVersionUID = 1L;

    @TableField("category_id")
    private String categoryId;

    @TableField("category_name")
    private String categoryName;


    @Override
    public Serializable pkVal() {
        return id;
    }

}
