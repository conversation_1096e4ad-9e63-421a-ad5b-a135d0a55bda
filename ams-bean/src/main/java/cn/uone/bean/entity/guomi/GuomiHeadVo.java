package cn.uone.bean.entity.guomi;

import cn.hutool.core.date.DateUtil;
import lombok.Data;

@Data
public class GuomiHeadVo {
    /**
     * 信息格式版本号
     */
    String LH_VERSION = "100";
    /**
     * 报文类型
     */
    String LH_TYPE = "0200";
    /**
     * 请求系统编码
     */
    String LH_REQ_SYS_NO = "000000MJ";
    /**
     * 响应系统编码
     */
    String LH_RES_SYS_NO = "A3031";
    /**
     * 中介编号
     */
    String LH_AGENT_NO;
    /**
     * 租赁企业编号
     */
    String LH_LEASING_COMPANY;
    /**
     * 省份编号
     */
    String LH_PROV_NO = "999999";
    /**
     * 城市编号
     */
    String LH_CITY_NO = "999999";
    /**
     * 区县编号
     */
    String LH_AREA_NO;
    /**
     * 渠道用户号
     */
    String LH_ORG_USER_ID;
    /**
     * 交易转发标志
     */
    String LH_FORWARD_FLAG = "0";
    /**
     * 请求日期
     */
    String LH_REQUEST_DATE = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss").substring(0, 8);
    /**
     * 请求时间
     */
    String LH_REQUEST_TIME = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss").substring(8, 14);
    /**
     * 响应日期
     */
    String LH_RESPONSE_DATE;
    /**
     * 响应时间
     */
    String LH_RESPONSE_TIME;
    /**
     * 原始请求流水号
     */
    String LH_REQUEST_FLOW_NO;
    /**
     * 服务方流水号
     */
    String LH_RESPONSE_FLOW_NO;
    /**
     * 交易代码
     */
    String LH_TRAN_CD;
    /**
     * 用户交易终端地址
     */
    String LH_TERM_INF;
    /**
     * 发起方安全节点加密的节点
     */
    String LH_ORG_NODE_ID;
    /**
     * 目标方安全节点加密的节点
     */
    String LH_DES_NODE_ID;
    /**
     * 终端序列号
     */
    String LH_ORG_TERM_SRL;
    /**
     * 交易状态
     */
    String LH_RESPONSE_STATUS;
    /**
     * 返回码
     */
    String LH_RESPONSE_CODE;
    /**
     * 返回信息描述
     */
    String LH_RESPONSE_MSG;
}
