package cn.uone.bean.entity.tpi.fadada;

import lombok.Data;

@Data
public class ReqExtsign {
    private String transactionId;//交易号
    private String contractId;//合同ID
    private String contractCode;//合同编号
    private String customerId;//客户编号
    private String docTitle;//文档标题
    private String signKeyword;//定位关键字
    private String keywordStrategy;//关键字签章策略：0：所有关键字签章 （默认）；1：第一个关键字签章 ；2：最后一个关键字签章；
    private String returnUrl;//页面跳转URL（签署结果同步通知）
    private String notifyUrl;//签署结果异步通知URL
}
