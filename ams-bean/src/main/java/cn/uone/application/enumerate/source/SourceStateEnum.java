package cn.uone.application.enumerate.source;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public enum SourceStateEnum {


    UNRENT("0", "未出租"),

    BOOKED("1", "已预定"),
    RENT("2", "已出租"),
    temporary("6", "临时用房"),
//    SELL("3", "已售卖"),
//    REPAIRS("4", "报修中"),
//    CANCEL("5", "已废弃"),
    ;

    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    SourceStateEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        SourceStateEnum[] enums = SourceStateEnum.values();
        for (SourceStateEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (SourceStateEnum enumerate : SourceStateEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
