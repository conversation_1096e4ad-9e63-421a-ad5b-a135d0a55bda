package cn.uone.application.enumerate.base;


public enum BizTypeEnum {

    BIZ01("101", "销售客户"),
    BIZ02("102", "客户服务"),
    BIZ03("103", "安全巡查"),
    BIZ04("104", "支付首期租金账单短信通知"),
    BIZ05("105", "退房短信通知"),
    BIZ06("106", "待审核签约-消息中心提醒"),
    BIZ07("107", "预定提醒-提消息中心提醒"),
    BIZ08("108", "退房申请-消息中心提醒"),
    BIZ09("109", "待办入住提醒-消息中心提醒");

    private String value;
    private String name;

    BizTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        BizTypeEnum[] enums = BizTypeEnum.values();
        for (BizTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
