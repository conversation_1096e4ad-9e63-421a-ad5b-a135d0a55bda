package cn.uone.application.enumerate.contract;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: ljl
 * @Date: 2019/1/17 19:41
 * @Description:
 */
public enum CheckOutTypeEnum {

    CHECKOUT("0", "退房"),
    ORGANIZATION("1", "员工搬离"),
    CHANG("3", "换房"),
    RELET("2", "续租"),
    EARLYCHECKOUT("4", "提前退房"),
    DEFAULTCHECKOUT("5", "违约退房"),
    ZHUANZU("6", "转租"),;

    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    CheckOutTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        CheckOutTypeEnum[] enums = CheckOutTypeEnum.values();
        for (CheckOutTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (CheckOutTypeEnum enumerate : CheckOutTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
