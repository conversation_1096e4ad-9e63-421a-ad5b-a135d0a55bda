package cn.uone.application.enumerate.contract;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 预约入住状态枚举
 * 待办理10，已取消20，已过期30，已完成40
 *
 * <AUTHOR>
 * @date 2018-12-15 17:48
 */
public enum ReservationStatusEnum {

    DAICHULI("10", "待办理"),

    YIQUXIAO("20", "已取消"),

    YIGUOQI("30", "已过期"),

    YIWANCHENG("40", "已完成");

    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    ReservationStatusEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (ReservationStatusEnum enumerate : ReservationStatusEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
