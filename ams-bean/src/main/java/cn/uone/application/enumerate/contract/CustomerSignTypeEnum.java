package cn.uone.application.enumerate.contract;

import cn.uone.application.enumerate.source.SourceTypeEnum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.uone.application.enumerate.source.SourceTypeEnum.HOUSE;

public enum CustomerSignTypeEnum {

    // 公寓合同
    APARTMENT("1", "公寓"),
    // 商业合同
    BUSINESS("2", "商业"),
    // 车位合同
    CARPORT("3", "车位");

    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    CustomerSignTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        CustomerSignTypeEnum[] enums = CustomerSignTypeEnum.values();
        for (CustomerSignTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (CustomerSignTypeEnum enumerate : CustomerSignTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public static SourceTypeEnum getSourceEnumByValue(String value) {
        SourceTypeEnum e = null;
        CustomerSignTypeEnum[] enums = CustomerSignTypeEnum.values();
        for (CustomerSignTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                e = getSourceEnumByEnum(temp);
                break;
            }
        }
        return e;
    }

    public static SourceTypeEnum getSourceEnumByEnum(CustomerSignTypeEnum itemTypeEnum) {
        SourceTypeEnum type = null;
        switch (itemTypeEnum) {
            case APARTMENT:
                type = HOUSE;
                break;
//            case BUSINESS:
//                type = SourceTypeEnum.BUSINESS;
//                break;
//            case CARPORT:
//                type = SourceTypeEnum.CAR;
//                break;
        }

        return type;

    }

    public static CustomerSignTypeEnum getEnumBySourceTypeEnum(SourceTypeEnum itemTypeEnum) {
        CustomerSignTypeEnum type = null;
        switch (itemTypeEnum) {
            case HOUSE:
                type = CustomerSignTypeEnum.APARTMENT;
                break;
           /* case BUSINESS:
                type = CustomerSignTypeEnum.BUSINESS;
                break;
            case CAR:
                type = CustomerSignTypeEnum.CARPORT;
                break;*/
        }

        return type;

    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
