package cn.uone.application.enumerate.contract;

import cn.uone.application.enumerate.source.SourceTypeEnum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: ljl
 * @Date: 2018/12/17 11:07
 * @Description:
 */
public enum ContractTempletTypeEnum {

    // 公寓
    APARTMENT("1", "公寓"),
    // 商业
    COMMERCE("2", "商业"),
    // 车位
    CARPORT("3", "车位"),
    // 公区
    COMMON("4", "公区"),
    // 办公
    WORK("5", "办公"),
    // 宠物
    PET("8", "门店"),
    HOTELS("9", "酒店"),
    PLANT("10", "厂房"),
    DORM("11", "宿舍"),
    COMP("12", "摊位"),

    CHECKOUT("6", "公寓退租"),

    CARCHECKOUT("7", "车位退租");


    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    ContractTempletTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static ContractTempletTypeEnum getEnumByValue(String value) {
        ContractTempletTypeEnum[] enums = ContractTempletTypeEnum.values();
        for (ContractTempletTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                return temp;
            }
        }
        return null;
    }

    public static String getNameByValue(String value) {
        String name = "";
        ContractTempletTypeEnum[] enums = ContractTempletTypeEnum.values();
        for (ContractTempletTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static String getValueByName(String name) {
        String value = "";
        ContractTempletTypeEnum[] enums = ContractTempletTypeEnum.values();
        for (ContractTempletTypeEnum temp : enums) {
            if (temp.getName().equals(name)) {
                value = temp.getValue();
                break;
            }
        }
        return value;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (ContractTempletTypeEnum enumerate : ContractTempletTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }


    public static SourceTypeEnum getSourceTypeEnumByEnum(ContractTempletTypeEnum sourceType) {
        SourceTypeEnum type = null;
        switch (sourceType) {
            case APARTMENT:
                type = SourceTypeEnum.HOUSE;
                break;
            case COMMERCE:
                type = SourceTypeEnum.BUSINESS;
                break;
            case CARPORT:
                type = SourceTypeEnum.CAR;
                break;
            case WORK:
                type = SourceTypeEnum.OFFICE;
                break;
            case HOTELS:
                type = SourceTypeEnum.HOTEL;
                break;
            case PLANT:
                type = SourceTypeEnum.WORKSHOP;
                break;
            case DORM:
                type = SourceTypeEnum.DORMITORY;
                break;
            case COMP:
                type = SourceTypeEnum.COMPREHENSIVE;
                break;
        }

        return type;

    }


    public static ContractTempletTypeEnum getEnumBySourceTypeEnum(SourceTypeEnum sourceType) {
        ContractTempletTypeEnum type = null;
        switch (sourceType) {
            case HOUSE:
                type = APARTMENT;
                break;
            /*case BUSINESS:
                type = COMMERCE;
                break;
            case CAR:
                type = CARPORT;
                break;
            case OFFICE:
                type = WORK;
                break;*/
            default:
                type = APARTMENT;
                break;
        }

        return type;

    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
