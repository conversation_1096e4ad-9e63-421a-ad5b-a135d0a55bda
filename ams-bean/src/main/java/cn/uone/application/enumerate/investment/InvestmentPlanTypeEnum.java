package cn.uone.application.enumerate.investment;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 招商管理，招商计划类型字典码表
 *
 */
public enum InvestmentPlanTypeEnum {

    MONTHPLAN("1", "月度计划"),
    QUARTERLYPLAN("2", "季度计划"),
    YEARPLAN("3", "年度计划");
    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    InvestmentPlanTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        InvestmentPlanTypeEnum[] enums = InvestmentPlanTypeEnum.values();
        for (InvestmentPlanTypeEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static String getValueByName(String name) {
        String value = "";
        InvestmentPlanTypeEnum[] enums = InvestmentPlanTypeEnum.values();
        for (InvestmentPlanTypeEnum temp : enums) {
            if (temp.getName().equals(name)) {
                value = temp.getValue();
                break;
            }
        }
        return value;
    }

    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (InvestmentPlanTypeEnum enumerate : InvestmentPlanTypeEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
