package cn.uone.application.enumerate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: lzc
 * @Date: 2019/06/25 09:37
 * @Description:
 */
public enum SubsidyPersonEnum {

    HUOJU_MANAGEMENT("78fa7355b70b1740d409dd8d949f92cc", "火炬管委会"),
    HULI_TALENT("b1a2dd56565100d3be707973e6e8c376", "湖里区人才办");


    private static List<Map<String, Object>> list = new ArrayList<>();
    private String value;
    private String name;

    SubsidyPersonEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        String name = "";
        SubsidyPersonEnum[] enums = SubsidyPersonEnum.values();
        for (SubsidyPersonEnum temp : enums) {
            if (temp.getValue().equals(value)) {
                name = temp.getName();
                break;
            }
        }
        return name;
    }

    public static String getValueByName(String name) {
        String value = "";
        SubsidyPersonEnum[] enums = SubsidyPersonEnum.values();
        for (SubsidyPersonEnum temp : enums) {
            if (temp.getName().equals(name)) {
                value = temp.getValue();
                break;
            }
        }
        return value;
    }


    public static List<Map<String, Object>> toList() {
        if (list.isEmpty()) {
            for (SubsidyPersonEnum enumerate : SubsidyPersonEnum.values()) {
                Map<String, Object> enumMap = new HashMap<>();
                enumMap.put("name", enumerate.getName());
                enumMap.put("value", enumerate.getValue());
                list.add(enumMap);
            }
        }
        return list;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
