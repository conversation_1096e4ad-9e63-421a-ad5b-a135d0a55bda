package cn.uone.fegin.crm;

import cn.uone.bean.entity.crm.ExpenseProjectEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ams-crm")
public interface IExpenseProjectFegin {

    @RequestMapping(value = "/t-expense-project-entity/getById")
    ExpenseProjectEntity getById(@RequestParam("id") String id);

    @RequestMapping(value = "/t-expense-project-entity/getByCompanyName")
    ExpenseProjectEntity getByCompanyName(@RequestParam("companyName") String companyName);

    @RequestMapping(value = "/t-expense-project-entity/saveOrUpdate")
    boolean saveOrUpdate(@RequestBody ExpenseProjectEntity entity);

    @RequestMapping(value = "/t-expense-project-entity/isOverSourceNum")
    boolean isOverSourceNum(@RequestParam("projectId")String projectId,@RequestParam("addNum")int addNum);

    @RequestMapping(value = "/t-expense-project-entity/getByUserId")
    ExpenseProjectEntity getByUserId(@RequestParam("userId") String userId);
}
