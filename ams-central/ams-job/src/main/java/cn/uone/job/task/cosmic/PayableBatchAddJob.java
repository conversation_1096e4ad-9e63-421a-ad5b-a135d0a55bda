package cn.uone.job.task.cosmic;

import cn.uone.job.config.BaseJob;
import cn.uone.job.config.JobLable;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.util.Date;

@JobLable("金蝶财务应付单")
public class PayableBatchAddJob extends BaseJob {

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        //上一次调度的时间
        Date previousFireTime = context.getPreviousFireTime();
        //这一次调度的时间
        Date fireTime = context.getFireTime();
        //在这个时间范围内推送到金蝶应付单系统中(含头不含尾)
        this.resp = jobFegin.cosmicPayableBatchAdd(previousFireTime, fireTime);
        super.execute(context);
    }
} 