package cn.uone.job.entity;

import cn.hutool.core.util.StrUtil;
import cn.uone.job.util.QuartzUtil;
import cn.uone.web.base.BaseModel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("qrtz_job_history")
public class QrtzJobHistory extends BaseModel<QrtzJobHistory> {

    private static final long serialVersionUID = 1L;

    @TableField("job_name")
    private String jobName;

    @TableField("job_class")
    private String jobClass;

    @TableField("start_time")
    private Date startTime;

    @TableField("end_time")
    private Date endTime;

    @TableField("result")
    private Integer result;

    @TableField("params")
    private String params;

    @TableField("remarks")
    private String remarks;

    @TableField(exist = false)
    private String jobClassStr;

    public String getJobClassStr() {
        if (StrUtil.isNotBlank(this.jobClass)) {
            return QuartzUtil.getJobLableForJobClass(this.jobClass);
        }
        return jobClassStr;
    }
}
