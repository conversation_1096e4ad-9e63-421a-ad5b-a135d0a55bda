package cn.uone.job.task;

import cn.uone.job.config.BaseJob;
import cn.uone.job.config.JobLable;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

@JobLable("生成水电待缴账单")
public class BatchCostForPayJob extends BaseJob {
    public void execute(JobExecutionContext context) throws JobExecutionException {
        this.resp = jobFegin.costForPayTask("HTJS","1");
        super.execute(context);
    }
}
