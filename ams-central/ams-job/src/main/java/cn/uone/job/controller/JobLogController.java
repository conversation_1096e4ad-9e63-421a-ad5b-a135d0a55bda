package cn.uone.job.controller;

import cn.hutool.core.util.StrUtil;
import cn.uone.job.dao.HistoryDao;
import cn.uone.job.entity.QrtzJobHistory;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/job/log")
public class JobLogController extends BaseController {

    @Autowired
    HistoryDao historyDao;

    @RequestMapping(value = "/page", method = RequestMethod.GET)
    @ApiOperation("分页列表")
    public RestResponse page(Page page, QrtzJobHistory history) {
        QueryWrapper<QrtzJobHistory> wrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(history.getJobClass())) {
            wrapper = wrapper.eq("job_class", history.getJobClass());
        }
        if (null != history.getResult()) {
            wrapper = wrapper.eq("result", history.getResult());
        }
        if (StrUtil.isNotBlank(history.getJobClass())) {
            wrapper = wrapper.like("job_name", "%" + history.getJobName() + "%");
        }
        wrapper.orderByDesc("create_date");
        IPage page1 = historyDao.selectPage(page, wrapper);
        return RestResponse.success().setData(page1);
    }
}
