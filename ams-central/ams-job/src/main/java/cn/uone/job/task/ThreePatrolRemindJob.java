package cn.uone.job.task;

import cn.uone.job.config.BaseJob;
import cn.uone.job.config.JobLable;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

@JobLable("三级巡查快到期消息通知")
public class ThreePatrolRemindJob extends BaseJob {

    public void execute(JobExecutionContext context) throws JobExecutionException {
        this.resp = jobFegin.threePatrolRemind();
        super.execute(context);
    }

}
