package cn.uone.job.task;

import cn.uone.job.config.BaseJob;
import cn.uone.job.config.JobLable;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

@JobLable("运营详情出租情况生成")
public class BatchOperateInfoJob extends BaseJob {

    public void execute(JobExecutionContext context) throws JobExecutionException {
        this.resp = jobFegin.batchOperateInfo();
        super.execute(context);
    }
}
