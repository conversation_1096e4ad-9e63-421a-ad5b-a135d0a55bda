package cn.uone.job.task;

import cn.uone.job.config.BaseJob;
import cn.uone.job.config.JobLable;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

@JobLable("企业员工客户信息同步")
public class SyncCustomerInfoJob extends BaseJob {

    public void execute(JobExecutionContext context) throws JobExecutionException {
        this.resp = jobFegin.syncCustomerInfo();
        super.execute(context);
    }

}
