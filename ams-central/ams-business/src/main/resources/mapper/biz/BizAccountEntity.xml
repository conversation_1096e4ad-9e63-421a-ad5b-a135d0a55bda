<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.biz.dao.BizAccountDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.biz.BizAccountEntity">
    <result column="id" property="id" />
        <result column="renter_id" property="renterId" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="bank_id" property="bankId" />
        <result column="branch_id" property="branchId" />
        <result column="province_id" property="provinceId" />
        <result column="bank_branch" property="bankBranch" />
        <result column="remark" property="remark" />
        <result column="is_del" property="isDel" />
        <result column="bank_code" property="bankCode" />
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,renter_id, code, name, bank_id, bank_branch, remark, is_del,bank_code,create_by,create_date,update_by,update_date,province_id,branch_id
    </sql>

    <select id="queryList" resultType ="cn.uone.bean.entity.business.biz.BizAccountEntity">
        select a.*,a.bank_branch as bankName from t_biz_account a
        where 1=1
        <if test="map.renterId != null and map.renterId != ''">
            and renter_id = #{map.renterId}
        </if>
        <if test="map.isDel != null and map.isDel != ''">
            and is_del = #{map.isDel}
        </if>
    </select>

    <select id="getBankCard" resultType ="cn.uone.bean.entity.business.biz.BizAccountEntity">
        select a.* from t_biz_account a
        where 1=1
        <if test="map.renterId != null and map.renterId != ''">
            and renter_id = #{map.renterId}
        </if>
        <if test="map.isDel != null and map.isDel != ''">
            and is_del = #{map.isDel}
        </if>
        <if test="map.userName != null and map.userName != ''">
            and name = #{map.userName}
        </if>
    </select>
</mapper>
