<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.demo.dao.StudentDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.demo.StudentEntity">
        <id column="stu_id" property="stuId" />
        <result column="stud_name" property="studName" />
        <result column="stu_age" property="stuAge" />
        <result column="stu_sex" property="stuSex" />
    </resultMap>

    <resultMap id="ScoreResultMap" type="cn.uone.business.demo.vo.StudentVo">
        <result column="stu_id" property="stuId" />
        <result column="stud_name" property="studName" />
        <result column="c_name" property="cname" />
        <result column="score" property="score" />
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        stu_id, stud_name, stu_age, stu_sex
    </sql>
    
    <update id="updateStudent">
      update demo_student
      set
      <if test="studName!=null">
      stud_name=#{studName}
      </if>
      where stu_id=#{stuId}
    </update>

    <select id="findStuScore" resultMap="ScoreResultMap">
        select st.stu_id,st.stud_name,c.c_name,sc.score
        from demo_student st
        left join demo_score sc on st.stu_id=sc.stu_id
        left join demo_course c on c.c_id=sc.c_id
        where st.stu_id=#{stuId} and c.c_name=#{cname}
    </select>
</mapper>
