<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.res.dao.ResCostConfigureDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.res.ResCostConfigureEntity">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="project_id" property="projectId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="illustration" property="illustration"/>
        <result column="source_type" property="sourceType"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        project_id,
        name,
        type,
        illustration,
        source_type
    </sql>

    <delete id="delectWithConfigureDetail" parameterType="String">
        delete from  t_res_cost_configure t
        inner join t_res_cost_configure_detail detail on t.id=detail.cost_configure_id
        where 1=1 and t.project_id=#{id}
    </delete>

    <sql id="selectCostAll">
        DISTINCT
        A.id,
        A.create_by,
        A.create_date,
        A.update_by,
        A.update_date,
        A.project_id,
        A.name,
        A.type,
        A.illustration,
        A.source_type,
        B.cost_type,
        C.source_type,
        C.price,
        C.lower_limit,
        C.top_limit,
        BM.billing_code
    </sql>

    <select id="querycostAll" resultType="cn.uone.bean.entity.business.res.vo.ResCostConfigureVo">
        SELECT
          <include refid="selectCostAll"/>
        FROM t_res_cost_configure A
        LEFT JOIN t_res_cost_configure_detail B ON B.cost_configure_id = A.id
        LEFT JOIN t_res_cost_ladder C ON C.configure_detail_id = B.id
        LEFT JOIN t_sys_billing_method BM ON BM.id = C.billing_method
        WHERE 1=1
        <if test="map.projectId != null and map.projectId != ''">
            and A.project_id = #{map.projectId}
        </if>
        <if test="map.costType != null and map.costType != ''">
            and B.cost_type = #{map.costType}
        </if>
        <if test="map.id != null and map.id != ''">
            and A.id = #{map.id}
        </if>
    </select>

    <select id="queryByCostConfigureId" resultType="cn.uone.bean.entity.business.res.vo.ResCostConfigureVo">
        SELECT
        A.id,
        A.create_by,
        A.create_date,
        A.update_by,
        A.update_date,
        A.project_id,
        A.name,
        A.type,
        A.illustration,
        A.source_type,
        B.cost_type,
        C.source_type,
        C.price,
        GROUP_CONCAT(distinct C.price SEPARATOR '/') price_str,
        C.lower_limit,
        C.top_limit,
        BM.billing_code
        FROM t_res_cost_configure A
        LEFT JOIN t_res_cost_configure_detail B ON B.cost_configure_id = A.id
        LEFT JOIN t_res_cost_ladder C ON C.configure_detail_id = B.id
        LEFT JOIN t_sys_billing_method BM ON BM.id = C.billing_method
        WHERE 1=1
        and C.is_cost ='1'
        and A.id = #{costConfigureId}
        and C.price>0
        group by B.cost_type
    </select>


    <select id="selectCostByIdAndTypeAndSourceType" resultType="cn.uone.bean.entity.business.res.vo.ResCostVo">
        select c.id,cd.cost_type,cl.source_type,
        cl.billing_method,cl.price,cl.top_limit,cl.lower_limit,cl.is_cost
        from t_res_cost_configure c,t_res_cost_configure_detail cd,t_res_cost_ladder cl
        where c.id = cd.cost_configure_id and cl.configure_detail_id = cd.id
        <if test="map.costId != null and map.costId != ''">
            and c.id = #{map.costId}
        </if>
        <if test="map.type != null and map.type != ''">
            and cd.cost_type = #{map.type}
        </if>
        <if test="map.sourceType != null and map.sourceType != ''">
            and cl.source_type = #{map.sourceType}
        </if>

        <if test="map.lim != null and map.lim != ''">
            and lower_limit &lt;= #{map.lim }
        </if>
        order by cl.lower_limit
    </select>


    <select id="getShowCost" resultType="java.lang.String">
        select group_concat(d.cost_type) from t_res_cost_configure c
        LEFT JOIN t_res_cost_configure_detail d on d.cost_configure_id = c.id
        LEFT JOIN t_res_cost_ladder l on l.configure_detail_id = d.id
        where l.price > 0  and l.is_cost ='1'  and c.id = #{id}
    </select>

</mapper>
