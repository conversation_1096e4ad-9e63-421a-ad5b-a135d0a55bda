<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.equ.dao.EquDeviceDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.equ.EquDeviceEntity">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="asset_code" property="assetCode" />
        <result column="device_code" property="deviceCode" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="model" property="model" />
        <result column="specs" property="specs" />
        <result column="status" property="status" />
        <result column="use_address" property="useAddress" />
        <result column="price" property="price" />
        <result column="warranty" property="warranty" />
        <result column="supplier_id" property="supplierId" />
        <result column="produce_date" property="produceDate" />
        <result column="install_date" property="installDate" />
        <result column="remark" property="remark" />
        <result column="create_date" property="createDate" />
        <result column="supplierName" property="supplierName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.update_by,
        t.update_date,
        t.asset_code, t.device_code, t.name, t.type, t.model, t.specs, t.status, t.use_address, t.price, t.warranty, t.supplier_id, t.produce_date, t.install_date, t.remark, t.create_date
    </sql>

    <select id="list" resultType="cn.uone.bean.entity.business.equ.EquDeviceEntity">
        select <include refid="Base_Column_List"/>,s.name supplierName from t_equ_device t
        left join t_equ_supplier s on s.id = t.supplier_id
        where 1=1
        #project_datascope#
        <if test="t.assetCode != null and t.assetCode != ''">
            AND t.asset_code LIKE CONCAT('%', #{t.assetCode}, '%')
        </if>
        <if test="t.deviceCode != null and t.deviceCode != ''">
            AND t.device_code LIKE CONCAT('%', #{t.deviceCode}, '%')
        </if>
        <if test="t.name != null and t.name != ''">
            AND t.name LIKE CONCAT('%', #{t.name}, '%')
        </if>
        <if test="t.supplierId != null and t.supplierId != ''">
            AND t.supplier_id = #{t.supplierId}
        </if>
        <if test="t.type != null and t.type != ''">
            AND t.type LIKE CONCAT('%', #{t.type}, '%')
        </if>
        <if test="t.status != null and t.status != ''">
            AND t.status = #{t.status}
        </if>
        <if test="t.keyWord != null and t.keyWord != '' and t.keyWord != '报修中' and t.keyWord != '使用中' and t.keyWord != '故障'">
            and (
            t.name LIKE CONCAT('%', #{t.keyWord}, '%')
            or t.use_address  LIKE CONCAT('%', #{t.keyWord}, '%')
            or t.type  LIKE CONCAT('%', #{t.keyWord}, '%')
            )
        </if>
        <if test="t.keyWord== '使用中'">
            AND t.status = 1
        </if>
        <if test="t.keyWord== '故障'">
            AND t.status = 2
        </if>
        <if test="t.keyWord== '报修中'">
            AND t.status = 3
        </if>
        ORDER BY t.update_date DESC
    </select>


</mapper>
