<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.rpt.dao.ReportUnPayDao">


    <select id="queryList" resultType="cn.uone.bean.entity.business.report.vo.UnPayVo">
        select
        o.id as orderId,
        o.code as orderCode,
        o.order_type as orderType,
        o.payable_time as orderDate,
        (case when DATE_FORMAT(now(),'%Y-%m-%d') >DATE_FORMAT(o.payable_time,'%Y-%m-%d') then '逾期' else '未支付' end) as payState,
        oit.payment as payment,
        oit.order_item_type as orderItemType,
        ci.name as signerName,
        s.source_name as location
        from (select order_id,sum(oi.payment) as payment,order_item_type,arrive_time from t_bil_order_item oi where  (oi.order_item_type in ('20') and oi.payment>0) or oi.order_item_type ='360' GROUP BY order_id) as oit
        LEFT JOIN t_bil_order o on oit.order_id = o.id
        LEFT JOIN t_cont_contract c on c.id = o.contract_id
        INNER JOIN v_res_source s on o.source_id = s.id
        LEFT JOIN t_cont_contract_info ci on c.id = ci.contract_id
        LEFT JOIN t_res_plan_partition pp on pp.id = s.partition_id
        where 1=1 and o.pay_state in ('10','30') and o.is_discard='0' and o.is_push='1' and  DATE_FORMAT(now(),'%Y-%m-%d') &gt;= DATE_FORMAT(o.payable_time,'%Y-%m-%d')
        <if test="search.projectId != null and search.projectId != ''">
            and s.project_id = #{search.projectId}
        </if>
        <if test="search.partitionId != null and search.partitionId != ''">
            and s.partition_id = #{search.partitionId}
        </if>
        <if test="search.propertyNature != null and search.propertyNature != ''">
            and pp.property_nature = #{search.propertyNature}
        </if>
        <if test="search.contractCode != null and search.contractCode != ''">
            and c.contract_code like CONCAT('%', #{search.contractCode}, '%')
        </if>
        <if test="search.orderCode != null and search.orderCode != ''">
            and o.code like CONCAT('%', #{search.orderCode}, '%')
        </if>
        <if test="search.keyWord != null and search.keyWord != ''">
            and (s.source_name like CONCAT('%', #{search.keyWord}, '%') or ci.name like CONCAT('%', #{search.keyWord}, '%'))
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            AND <![CDATA[date(o.payable_time) >= date(#{search.startDate}) ]]>
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            AND <![CDATA[date(o.payable_time) <= date(#{search.endDate}) ]]>
        </if>
        <if test="search.orderIds != null and search.orderIds.size() > 0">
            AND o.id in
            <foreach collection="search.orderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by o.id
        order by o.payable_time desc
    </select>

    <select id="getTotalUnpay" resultType="java.math.BigDecimal">
        select sum(unpay.payment) from
        (select
        o.id as orderId,
        o.code as orderCode,
        o.order_type as orderType,
        o.payable_time as orderDate,
        oit.payment as payment,
        oit.order_item_type as orderItemType,
        ci.name as signerName,
        s.source_name as location
        from (select order_id,sum(oi.payment) as payment,order_item_type,arrive_time from t_bil_order_item oi where  oi.order_item_type in ('20') and oi.payment>0 GROUP BY order_id) as oit
        LEFT JOIN t_bil_order o on oit.order_id = o.id
        LEFT JOIN t_cont_contract c on c.id = o.contract_id
        INNER JOIN v_res_source s on o.source_id = s.id
        LEFT JOIN t_cont_contract_info ci on c.id = ci.contract_id
        LEFT JOIN t_res_plan_partition pp on pp.id = s.partition_id
        where 1=1 and o.pay_state in ('10','30') and o.is_discard='0' and o.is_push='1' and DATE_FORMAT(now(),'%Y-%m-%d') &gt;= DATE_FORMAT(o.payable_time,'%Y-%m-%d')
        <if test="search.projectId != null and search.projectId != ''">
            and s.project_id = #{search.projectId}
        </if>
        <if test="search.partitionId != null and search.partitionId != ''">
            and s.partition_id = #{search.partitionId}
        </if>
        <if test="search.propertyNature != null and search.propertyNature != ''">
            and pp.property_nature = #{search.propertyNature}
        </if>
        <if test="search.contractCode != null and search.contractCode != ''">
            and c.contract_code like CONCAT('%', #{search.contractCode}, '%')
        </if>
        <if test="search.orderCode != null and search.orderCode != ''">
            and o.code like CONCAT('%', #{search.orderCode}, '%')
        </if>
        <if test="search.keyWord != null and search.keyWord != ''">
            and (s.source_name like CONCAT('%', #{search.keyWord}, '%') or ci.name like CONCAT('%', #{search.keyWord}, '%'))
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            AND <![CDATA[date(o.payable_time) >= date(#{search.startDate}) ]]>
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            AND <![CDATA[date(o.payable_time) <= date(#{search.endDate}) ]]>
        </if>
        <if test="search.orderIds != null and search.orderIds.size() > 0">
            AND o.id in
            <foreach collection="search.orderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by o.id
        order by o.payable_time desc) unpay
    </select>

</mapper>
