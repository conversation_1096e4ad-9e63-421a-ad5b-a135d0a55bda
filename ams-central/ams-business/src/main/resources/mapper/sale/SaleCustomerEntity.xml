<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.sale.dao.SaleCustomerDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.sale.SaleCustomerEntity">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="renter_id" property="renterId"/>
        <result column="level" property="level"/>
        <result column="customer_type" property="customerType"/>
        <result column="company" property="company"/>
        <result column="origin" property="origin"/>
        <result column="project_id" property="projectId"/>
        <result column="area_id" property="areaId"/>
        <result column="source_id" property="sourceId"/>
        <result column="visit_time" property="visitTime"/>
        <result column="check_in_time" property="checkInTime"/>
        <result column="pay_type" property="payType"/>
        <result column="summary" property="summary"/>
        <result column="manager" property="manager"/>
        <result column="manager_id" property="managerId"/>
        <result column="state" property="state"/>
        <result column="creater" property="creater"/>
        <result column="finish_date" property="finishDate"/>
    </resultMap>


    <resultMap id="CustomerMap" type="cn.uone.bean.entity.business.sale.vo.CustomerVo">
        <result column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="area_id" property="areaId"/>
        <result column="source_id" property="sourceId"/>
        <result column="name" property="name"/>
        <result column="tel" property="tel"/>
        <result column="sex" property="sex"/>
        <result column="customer_type" property="type"/>
        <result column="customer_type" property="typeValue"/>
        <result column="company" property="company"/>
        <result column="level" property="level"/>
        <result column="level" property="levelValue"/>
        <result column="project" property="project"/>
        <result column="visit_time" property="visitTime"/>
        <result column="creater" property="creater"/>
        <result column="create_date" property="createDate"/>
        <result column="manager" property="manager"/>
        <result column="finish_date" property="finishDate"/>
        <result column="origin" property="origin"/>
        <result column="district_id" property="districtId"/>
        <result column="state" property="state"/>
        <result column="state" property="stateValue"/>
        <result column="cont_num" property="contNum"/>
        <result column="source_num" property="sourceNum"/>
        <result column="house_type" property="houseType"/>
        <result column="num" property="num"/>
        <result column="lease_term" property="leaseTerm"/>
        <result column="budget" property="budget"/>
        <result column="hxlx" property="hxlx"/>
        <result column="check_in_time" property="checkInTime"/>
        <result column="is_verify" property="isVerify"/>
        <result column="renter_id" property="renterId"/>
        <result column="district" property="district"/>
    </resultMap>


    <resultMap id="CustomerContSouMap" type="cn.uone.bean.entity.business.sale.vo.CustomerContSouVo">
        <result column="contract_id" property="contractId"/>
        <result column="source_id" property="sourceId"/>
        <result column="customer_id" property="customerId"/>
        <result column="contract_code" property="contractCode"/>
        <result column="source_name" property="sourceName"/>
        <result column="pay_time" property="payTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        renter_id, level, customer_type, company, origin, project_id, area_id, source_id, visit_time, check_in_time, pay_type, summary,manager,manager_id,state,
        finish_date,creater

    </sql>

    <select id="selectPageByMap" resultMap="CustomerMap">
        select t.* from
        (select sc.renter_id,
        sc.id,vsr.name,vsr.tel,vsr.sex,sc.customer_type,sc.company,sc.`level`,sc.project_id,sc.area_id,sc.source_id,
			rp.name project,sc.visit_time,sc.creater,sc.create_date,sc.manager,sc.finish_date,sc.origin,
        sd.district_id,sd.num,sd.lease_term,sd.check_in_time,sd.budget,sd.house_type, CONCAT(rtp.room,"室",rtp.hall,"厅",rtp.toilet,"卫") as hxlx,
        sc.state,ifnull(cont.num,0) cont_num,ifnull(s.num,0) source_num,sc.summary,log.info,log.type
        log_type,log.operator,vsr.is_verify,rs.id as sourceId,rs.source_name as sourceName,rs.partition_id as partitionId
        from t_sale_demand sd,v_sys_renter vsr,t_sale_customer sc
        left join t_res_project rp on rp.id = sc.project_id
        left join (select customer_id,count(1) num from t_sale_customer_contract_rel group by customer_id) cont on
        cont.customer_id = sc.id
        left join (select count(1) num,c.customer_id from t_sale_customer_contract_rel c,t_cont_contract_source_rel cs
        where c.contract_id=cs.contract_id group by c.customer_id) s on s.customer_id=sc.id
        left join (select * from (select info,type,operator,customer_id from t_sale_customer_log group by id order by create_date
        desc) t group by t.customer_id) log on log.customer_id = sc.id
        left join v_res_source rs on rs.id=sc.source_id
        left join t_res_house_type rtp on rtp.id = rs.house_type_id
        where sc.renter_id=vsr.id and sd.customer_id = sc.id #project_datascope#
        <if test="customer.id!=null and customer.id!=''">
            AND sc.id = #{customer.id}
        </if>

        <if test="customer.projectId != null and customer.projectId != ''">
            AND sc.project_id = #{customer.projectId}
        </if>
        <if test="customer.houseType != null and customer.houseType != ''">
            AND sd.house_type = #{customer.houseType}
        </if>
        <if test="customer.origin != null and customer.origin != ''">
            AND sc.origin = #{customer.origin}
        </if>
        <if test="customer.customerType != null and customer.customerType != ''">
            AND sc.customer_type = #{customer.customerType}
        </if>
        <if test="customer.state != null and customer.state != ''">
            AND sc.state = #{customer.state}
        </if>
        <if test="customer.statein != null and customer.statein != ''">
            AND sc.state in
            <foreach collection="customer.statein" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="customer.level != null and customer.level != ''">
            AND sc.level = #{customer.level}
        </if>
        <if test="customer.manager != null and customer.manager != ''">
            AND sc.manager like CONCAT('%', #{customer.manager}, '%')
        </if>
        <if test="customer.managerId != null and customer.managerId != ''">
            AND sc.manager_id = #{customer.managerId}
        </if>
        <if test="customer.isFinish != null">
            <if test="customer.isFinish">
                and sc.finish_date is not null
            </if>
            <if test="!customer.isFinish">
                and sc.finish_date is null
            </if>
        </if>

        <if test="customer.tel != null and customer.tel != ''">
            AND vsr.tel like CONCAT('%', #{customer.tel}, '%')
        </if>
        <if test="customer.creater != null and customer.creater != ''">
            AND sc.creater like CONCAT('%', #{customer.creater}, '%')
        </if>
        <if test="customer.createStartTime != null and customer.createStartTime != ''">
            AND DATE_FORMAT(sc.create_date,'%Y-%m-%d') &gt;= #{customer.createStartTime}
        </if>

        <if test="customer.createEndTime != null and customer.createEndTime != ''">
            AND DATE_FORMAT(sc.create_date,'%Y-%m-%d') &lt;= #{customer.createEndTime}
        </if>

        <if test="customer.visitStartTime != null and customer.visitStartTime != ''">
            AND DATE_FORMAT(sc.visit_time,'%Y-%m-%d') &gt;= #{customer.visitStartTime}
        </if>

        <if test="customer.visitEndTime != null and customer.visitEndTime != ''">
            AND DATE_FORMAT(sc.visit_time,'%Y-%m-%d') &lt;= #{customer.visitEndTime}
        </if>
        <if test="customer.finishStartTime != null and customer.finishStartTime != ''">
            AND DATE_FORMAT(sc.finish_date,'%Y-%m-%d') &gt;= #{customer.finishStartTime}
        </if>

        <if test="customer.finishEndTime != null and customer.finishEndTime != ''">
            AND DATE_FORMAT(sc.finish_date,'%Y-%m-%d') &lt;= #{customer.finishEndTime}
        </if>
        group by sc.id
        order by sc.update_date desc) t
        group by t.renter_id
    </select>

    <select id="selectContByCustomerId" resultMap="CustomerContSouMap">
        select scc.contract_id,scc.customer_id,con.contract_code
        from t_sale_customer_contract_rel scc,t_cont_contract con
        where scc.contract_id = con.id and scc.customer_id = #{customerId}
    </select>


    <select id="selectSourceByCustomerId" resultMap="CustomerContSouMap">
        select scc.customer_id,scc.contract_id,s.id as source_id ,con.contract_code,s.source_name,o.pay_time
        from t_sale_customer_contract_rel scc,t_cont_contract_source_rel cs,v_res_source s,t_cont_contract con
        left join (select * from (select id,contract_id,pay_time from t_bil_order where order_type = '20' order by create_date) t
        group by t.contract_id) o on o.contract_id = con.id
        where scc.contract_id = cs.contract_id and cs.source_id = s.id and scc.contract_id = con.id and scc.customer_id = #{customerId}
    </select>

    <select id="countCustomerNum" resultType="cn.uone.bean.entity.business.sale.vo.CustomerCountVo">
        SELECT IFNULL(SUM(IF(tt.state='10',1,0)),0) waitNum,
            IFNULL(SUM(IF(tt.state='10' AND LEVEL ='1',1,0)),0) l1NUm,
            IFNULL(SUM(IF(tt.state='10' AND LEVEL ='2',1,0)),0) l2NUm,
            IFNULL(SUM(IF(tt.state='10' AND LEVEL ='3',1,0)),0) l3NUm,
            IFNULL(SUM(IF(tt.state ='20',1,0)),0) processedNum,
            IFNULL(SUM(IF(tt.state ='30',1,0)),0) appointmentNum,
            IFNULL(SUM(IF(tt.state ='40',1,0)),0) broughtNum,
            IFNULL(SUM(IF(tt.state ='50',1,0)),0) reservedNum,
            IFNULL(SUM(IF(tt.state ='55',1,0)),0) intentNum,
            IFNULL(SUM(IF(tt.state ='56',1,0)),0) signingNum,
            IFNULL(SUM(IF(tt.state ='60',1,0)),0) signedNum,
            IFNULL(SUM(IF(tt.state ='90',1,0)),0) invalidNum
        FROM (SELECT k.* FROM
        (SELECT t.* FROM t_sale_customer t WHERE EXISTS (SELECT vsr.id FROM v_sys_renter vsr WHERE t.renter_id=vsr.id)
                #project_datascope#
                <if test="managerId != null and managerId != ''">
                    AND (t.manager_id = #{managerId} or t.create_by = #{managerId})
                </if>
                <if test="projectId != null and projectId != ''">
                    AND t.project_id = #{projectId}
                </if>
                 GROUP BY t.id ORDER BY t.`update_date`) k
        GROUP BY k.renter_id) tt
    </select>

    <select id="getStatistics" resultType="cn.uone.bean.entity.business.sale.vo.CustomerCountVo">
        select ifnull(sum(if(state='10',1,0)),0) waitNum,
        ifnull(sum(if(state='10' and level ='1',1,0)),0) l1NUm,
        ifnull(sum(if(state='10' and level ='2',1,0)),0) l2NUm,
        ifnull(sum(if(state='10' and level ='3',1,0)),0) l3NUm,
        ifnull(sum(if(state ='20',1,0)),0) processedNum,
        ifnull(sum(if(state ='30',1,0)),0) appointmentNum,
        ifnull(sum(if(state ='40',1,0)),0) broughtNum,
        ifnull(sum(if(state in('40','10','20','30'),1,0)),0) reserveNum,
        ifnull(sum(if(state ='50',1,0)),0) reservedNum,
        ifnull(sum(if(state ='60',1,0)),0) signedNum,
        ifnull(sum(if(state ='90',1,0)),0) invalidNum
        from t_sale_customer t where EXISTS (select vsr.id from v_sys_renter vsr where t.renter_id=vsr.id)
        #project_datascope#
        AND t.project_id = #{map.projectId}
    </select>

    <select id="getDemandBySourceIdAndUserId" resultType="cn.uone.bean.entity.business.sale.SaleDemandEntity">
        select *
        from t_sale_demand t where 1=1
        <if test="sourceId != null and sourceId != ''">
            AND source_id = #{sourceId}
        </if>
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        order by create_date desc
        limit 1
    </select>

    <select id="getDemandById" resultType="cn.uone.bean.entity.business.sale.SaleDemandEntity">
        select *
        from t_sale_demand t where 1=1
        <if test="demandId != null and demandId != ''">
            AND t.id = #{demandId}
        </if>
    </select>

    <insert id="saveCustomerContractRel">
        insert into t_sale_customer_contract_rel (contract_id, customer_id,success_msg)
        values (#{contract_id,jdbcType=VARCHAR}, #{customer_id,jdbcType=VARCHAR},#{success_msg,jdbcType=VARCHAR})
    </insert>

    <select id="getCustomerContractRelByContractId" resultType="cn.uone.bean.entity.business.sale.vo.CustomerContSouVo">
        select contract_id,customer_id
        from t_sale_customer_contract_rel t where 1=1
        <if test="contractId != null and contractId != ''">
            AND contract_id = #{contractId}
        </if>
        <if test="customerId != null and customerId != ''">
            AND customer_id = #{customerId}
        </if>
    </select>

    <select id="findRenter" resultType="cn.uone.bean.entity.crm.RenterEntity">
        select * from v_sys_renter
        where 1=1
        <if test="map.id != null and map.id != ''">
            AND id = #{map.id}
        </if>

        <if test="map.tel != null and map.tel != ''">
            AND tel = #{map.tel}
        </if>
    </select>

    <select id="getCustomSourcesByRenderId" resultType="cn.uone.bean.entity.business.res.vo.ResProjectHotVo">
        SELECT p.name projectName,f.url pic,p.address AS address,
        CONCAT(h.room,"室",h.hall,"厅",h.toilet,"卫") AS info,
        MIN(sc.price) minPrice,
        MAX(sc.price) maxPrice,
        t.`visit_time` createDate
        FROM t_sale_customer t
        LEFT JOIN t_res_house_type h ON h.id=t.`area_id`
        LEFT JOIN t_res_project p ON p.id=t.`project_id`
        LEFT JOIN t_sys_file f ON f.`from_id`=p.id AND f.`type`='15'
        LEFT JOIN v_res_source s ON IF(t.`area_id` IS NULL,s.`project_id`=p.id,s.`house_type_id`=h.id)
        LEFT JOIN t_res_source_configure sc ON sc.source_id=s.id
        WHERE 1=1
        <if test="renterId != null and renterId != ''">
            AND t.renter_id = #{renterId}
        </if>
        GROUP BY t.id ORDER BY t.`create_date` DESC
    </select>

    <!--  获取今天已签约的人数  -->
    <select id="selectCountSigned" resultType="cn.uone.business.sys.vo.DataStatisticsVo">
        SELECT
        count(c.id) as signed
        FROM
        t_cont_contract c
        LEFT JOIN t_cont_contract_source_rel cs ON c.id = cs.contract_id
        LEFT JOIN v_res_source s ON cs.source_id = s.id
        WHERE
        1 = 1 AND c.state = '1'
        <if test="map.projectId != null and map.projectId != ''">
            AND s.project_id = #{map.projectId}
        </if>
        <if test="map.starTime != null and map.starTime != ''">
            and DATE_FORMAT(c.create_date,'%Y-%m-%d') >= #{map.starTime}
        </if>
        <if test="map.endTime != null and map.endTime != ''">
            and DATE_FORMAT(c.create_date,'%Y-%m-%d') &lt;= #{map.endTime}
        </if>
        ORDER BY
        c.create_date DESC
    </select>
    <!--  获取今天意向登记的人数  -->
    <select id="selectCountSign" resultType="cn.uone.business.sys.vo.DataStatisticsVo">
        SELECT
        count(sc.id) as sign
        FROM
        t_sale_demand sd,
        v_sys_renter vsr,
        t_sale_customer sc
        WHERE
        sc.renter_id = vsr.id
        AND sd.customer_id = sc.id
        <if test="map.projectId != null and map.projectId != ''">
            AND sc.project_id = #{map.projectId}
        </if>
        <if test="map.starTime != null and map.starTime != ''">
            and DATE_FORMAT(sc.create_date,'%Y-%m-%d') >= #{map.starTime}
        </if>
        <if test="map.endTime != null and map.endTime != ''">
            and DATE_FORMAT(sc.create_date,'%Y-%m-%d') &lt;= #{map.endTime}
        </if>
        ORDER BY
        sc.create_date DESC
    </select>
    <!--  获取报修未处理的总人数  -->
    <select id="selectRepair" resultType="cn.uone.business.sys.vo.DataStatisticsVo">
        SELECT
        count(r.id) as repair
        FROM
        t_res_repair r
        WHERE
        r.state &lt;4
        and r.type='1'
        <if test="map.projectId != null and map.projectId != ''">
            AND r.project_id = #{map.projectId}
        </if>
    </select>
    <!--  获取房源闲置和已出租的数量  -->
    <select id="selectHouse" resultType="cn.uone.business.sys.vo.DataStatisticsVo">
        SELECT
        count(if(A.state=0,true,null)) as house,
        count(if(A.state!=0,true,null)) as houseIdle
        FROM
        v_res_source A
        WHERE
        1 = 1
        AND is_short = '0'
        <if test="map.projectId != null and map.projectId != ''">
            AND A.project_id = #{map.projectId}
        </if>
    </select>

    <!--获取最近入住人员信息-->
    <select id="getCheckInUser" resultType="cn.uone.bean.entity.business.cont.ContCheckInUserEntity">
        SELECT
        A.*
        FROM
        `t_cont_check_in_user` AS A
        left join t_cont_contract_source_rel cs on A.contract_source_id=cs.id
        left join t_res_source rs on rs.id=cs.source_id
        WHERE 1=1
        and A.is_checkin = '1'
        <if test="map.projectId != null and map.projectId != ''">
            AND rs.project_id = #{map.projectId}
        </if>
        ORDER BY
        A.check_in_date DESC
        LIMIT 0,20
    </select>

    <!--获取一周内的新注册用户-->
    <select id="newUser" resultType="cn.uone.business.sys.vo.DataStatisticsVo">
        SELECT
	        DATE_FORMAT( create_date, '%Y-%m-%d' ) as weekTime,
	        count( create_date ) as newUser
        FROM
	        v_sys_renter
        WHERE
	        create_date >= #{map.starTime}
        GROUP BY
	        DATE_FORMAT( create_date, '%Y-%m-%d' ) = #{map.starTime},
	        DATE_FORMAT( create_date, '%Y-%m-%d' ) = date_add(#{map.starTime},interval 1 day),
	        DATE_FORMAT( create_date, '%Y-%m-%d' ) = date_add(#{map.starTime},interval 2 day),
	        DATE_FORMAT( create_date, '%Y-%m-%d' ) = date_add(#{map.starTime},interval 3 day),
	        DATE_FORMAT( create_date, '%Y-%m-%d' ) = date_add(#{map.starTime},interval 4 day),
	        DATE_FORMAT( create_date, '%Y-%m-%d' ) = date_add(#{map.starTime},interval 5 day),
	        DATE_FORMAT( create_date, '%Y-%m-%d' ) = date_add(#{map.starTime},interval 6 day)
	    ORDER BY
	        create_date
    </select>

    <select id="getPayment" resultType="cn.uone.business.sys.vo.DataStatisticsVo">
        SELECT
        a.item as weekTime,
        IFNULL( b.pay, 0 ) AS payment,
        IFNULL( b.VALUE, 0 ) AS actualPayment
        FROM
        (
        SELECT
        DATE_SUB( CURDATE( ), INTERVAL 1 DAY ) AS item UNION ALL
        SELECT
        DATE_SUB( CURDATE( ), INTERVAL 2 DAY ) AS item UNION ALL
        SELECT
        DATE_SUB( CURDATE( ), INTERVAL 3 DAY ) AS item UNION ALL
        SELECT
        DATE_SUB( CURDATE( ), INTERVAL 4 DAY ) AS item UNION ALL
        SELECT
        DATE_SUB( CURDATE( ), INTERVAL 5 DAY ) AS item UNION ALL
        SELECT
        DATE_SUB( CURDATE( ), INTERVAL 6 DAY ) AS item UNION ALL
        SELECT
        DATE_SUB( CURDATE( ), INTERVAL 7 DAY ) AS item UNION ALL
        SELECT
        DATE_SUB( CURDATE( ), INTERVAL 0 DAY ) AS item
        ) a
        LEFT JOIN (
        SELECT
        DATE( o.create_date ) AS date,
        sum( o.payment ) AS pay,
        sum( o.actual_payment ) AS VALUE
        FROM
        t_bil_order o,v_res_source s
        where
        pay_state='20'
        <if test="map.projectId != null and map.projectId != ''">
            AND project_id = #{map.projectId} and o.source_id=s.id
        </if>
        GROUP BY
        DATE( o.create_date )
        ) b ON a.item = b.date
        order by a.item
    </select>

    <!---->
    <select id="getRptContract" resultType="cn.uone.business.sys.vo.DataStatisticsVo">
        select
        state,count(id) as num
        from
        t_rpt_contract
        where
        1=1
        <if test="map.projectId != null and map.projectId != ''">
            AND project_id = #{map.projectId}
        </if>
        group by
        state
    </select>
    <select id="getConContract" resultType="cn.uone.business.sys.vo.DataStatisticsVo">
        SELECT
        A.state,
        count(A.id) as num
        FROM
        t_cont_contract A
        LEFT JOIN
        t_cont_contract_source_rel B ON A.id = B.contract_id
        LEFT JOIN
        v_res_source C ON B.source_id = C.id
        WHERE
        1 = 1
        <if test="map.projectId != null and map.projectId != ''">
            AND C.project_id = #{map.projectId}
        </if>
        AND ( A.is_short = '0' OR A.is_short IS NULL )
        GROUP BY state
    </select>

    <select id="selectManagerIdsByCount" resultType="java.lang.String">
        SELECT t.manager_id FROM
            (SELECT c.`manager_id`,COUNT(1) num FROM t_sale_customer_log l,t_sale_customer c
             WHERE l.`customer_id`=c.id
               AND c.`state` NOT IN('60','90') AND c.`manager_id` IS NOT NULL AND c.`project_id`=#{projectId}
             GROUP BY c.`manager_id`) t
        ORDER BY t.num
    </select>


    <select id="getBySourceId" resultType="cn.uone.bean.entity.business.sale.SaleCustomerEntity">
        SELECT *
        FROM t_sale_customer
        where source_id = #{sourceId}
        ORDER BY create_date desc
    </select>


</mapper>
