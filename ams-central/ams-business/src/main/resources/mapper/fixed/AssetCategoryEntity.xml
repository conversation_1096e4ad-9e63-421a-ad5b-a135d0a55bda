<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.fixed.dao.AssetCategoryDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.fixed.AssetCategoryEntity">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="category_code" property="categoryCode" />
        <result column="category_name" property="categoryName" />
        <result column="parent_id" property="parentId" />
        <result column="parent_name" property="parentName" />
        <result column="project_id" property="projectId" />
        <result column="remark" property="remark" />
        <result column="company_id" property="companyId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        company_id,category_code,category_name,parent_id,parent_name,project_id,remark
    </sql>

    <select id="getAllDept" resultType="java.util.Map">
        select id as value ,category_name as name
        from t_fixed_asset_category
        where
        1=1
        <if test="id!=null and id!=''">
            and id != #{id}
        </if>
    </select>
</mapper>
