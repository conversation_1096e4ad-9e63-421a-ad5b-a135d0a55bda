<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.bil.dao.EquipmentWarnDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.bil.EquipmentWarnEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="device_no" property="deviceNo" />
        <result column="type" property="type" />
        <result column="state" property="state" />
        <result column="status" property="status" />
    </resultMap>

    <resultMap id="EquipmentWarnVoMap" type="cn.uone.bean.entity.business.bil.vo.EquipmentWarnVo" extends="BaseResultMap">
        <result column="project_id" property="projectId"/>
        <result column="source_id" property="sourceId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        device_no, type, state, status
    </sql>



    <select id="selectEquipmentByMap" parameterType="java.util.Map" resultMap="EquipmentWarnVoMap">
        select * from t_equipment_warn t
        where 1=1
        <if test="map.searchVo.state != null and map.searchVo.state != ''">
            and t.state = #{map.searchVo.state}
        </if>
    </select>


    <select id="getEntityByUUid" resultType="cn.uone.bean.entity.business.bil.EquipmentWarnEntity">
        select * from t_equipment_warn t where t.state = '1' and t.device_no #{item.uuid}
    </select>

</mapper>
