<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.business.flow.dao.ActReProcdefDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.business.flow.ActReProcdefEntity">
        <id column="ID_" property="id" />
        <result column="REV_" property="rev" />
        <result column="CATEGORY_" property="category" />
        <result column="NAME_" property="name" />
        <result column="KEY_" property="key" />
        <result column="VERSION_" property="version" />
        <result column="DEPLOYMENT_ID_" property="deploymentId" />
        <result column="RESOURCE_NAME_" property="resourceName" />
        <result column="DGRM_RESOURCE_NAME_" property="dgrmResourceName" />
        <result column="DESCRIPTION_" property="description" />
        <result column="HAS_START_FORM_KEY_" property="hasStartFormKey" />
        <result column="HAS_GRAPHICAL_NOTATION_" property="hasGraphicalNotation" />
        <result column="SUSPENSION_STATE_" property="suspensionState" />
        <result column="TENANT_ID_" property="tenantId" />
        <result column="ENGINE_VERSION_" property="engineVersion" />
        <result column="DERIVED_FROM_" property="derivedFrom" />
        <result column="DERIVED_FROM_ROOT_" property="derivedFromRoot" />
        <result column="DERIVED_VERSION_" property="derivedVersion" />
        <result column="project_id" property="projectId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID_, REV_, CATEGORY_, NAME_, KEY_, VERSION_, DEPLOYMENT_ID_, RESOURCE_NAME_, DGRM_RESOURCE_NAME_, DESCRIPTION_, HAS_START_FORM_KEY_, HAS_GRAPHICAL_NOTATION_, SUSPENSION_STATE_, TENANT_ID_, ENGINE_VERSION_, DERIVED_FROM_, DERIVED_FROM_ROOT_, DERIVED_VERSION_,project_id
    </sql>

    <select id="selectDeployList" resultType="cn.uone.bean.entity.business.flow.vo.FlowProcDefDto">
        SELECT
        rp.id_ as id,
        rp.deployment_id_ as deploymentId,
        rd.name_ as name,
        rd.category_ as category,
        rp.key_ as flowKey,
        rp.version_ as version,
        rp.suspension_state_ as suspensionState,
        rd.deploy_time_  as deploymentTime,
        rp.project_id as projectId
        FROM
        act_re_procdef rp
        LEFT JOIN act_re_deployment rd ON rp.deployment_id_ = rd.id_
        <where>
            <if test="map.name != null and map.name != ''">
                and rd.name_ like concat('%', #{map.name}, '%')
            </if>
            <if test="map.projectId != null and map.projectId != ''">
                and rp.project_id = #{map.projectId}
            </if>
        </where>
        order by rd.deploy_time_ desc
    </select>
</mapper>
