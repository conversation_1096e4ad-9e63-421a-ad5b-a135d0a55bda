package cn.uone.business.unionPay;

import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.business.bil.service.IBilInterfaceMsgService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.common.controller.CommonUnionPayController;
import cn.uone.business.res.service.IResProjectService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.fegin.tpi.UnionPayFegin;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheParam;
import cn.uone.web.util.SafeCompute;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 从漳州城投工程迁移过来
 * <p>
 * 小程序订单支付 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-8
 * caizhanghe edit 2024-05-30
 */
@RestController
@RequestMapping("/order/unionPay")
public class OrderUnionPayController {

    private static final Logger log = LoggerFactory.getLogger(CommonUnionPayController.class);

    @Autowired
    private UnionPayFegin unionPayFegin;

    @Autowired
    IBilOrderService bilOrderService;

    @Autowired
    private IBilInterfaceMsgService bilInterfaceMsgService;

    @Autowired
    private IResProjectService resProjectService;

    @Autowired
    private IResSourceService sourceService;

    /**
     * 账单支付（单笔）
     * @param ids
     * @param openid
     * @return
     * @throws Exception
     */
    @RequestMapping("/singleUnionPay")
    @UonePermissions
    public RestResponse singleUnionPay(@RequestParam List<String> ids,@CacheParam String openid){
        BilOrderEntity order = bilOrderService.getById(ids.get(0));
        String orderCode = order.getCode();
        log.info("1.账单编号：" + orderCode + "用户发起支付！");
        RestResponse response = new RestResponse();
        String resp = null;
        Map<String,String> map = new HashMap<>();
        try {
            map = unionPayFegin.orderPay(getAmount(order.getPayment()),orderCode,openid);
            resp = map.get("resStr");
            System.out.println("返回结果:\n" + resp);
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject respJson = JSONObject.fromObject(resp);
        if (respJson.containsKey("targetStatus")) {
            String status = respJson.getString("targetStatus");
            bilInterfaceMsgService.addInterfaceMsg(map.get("requestMessage"), respJson.toString(), null, order.getCode(), "发起银联支付");
            if ("SUCCESS|SUCCESS".equals(status)) {
                String miniPayRequest = respJson.getString("miniPayRequest");
                response.setData(miniPayRequest);
                response.setSuccess(true);
                response.code(200);
                return response;
            } else {
                return response.setMessage("查询银联支付结果状态:" + status).setSuccess(false);
            }
        } else {
            log.info("银联返回消息：" + respJson);
            return response.setMessage("银联返回消息：" + respJson.getString("errMsg")).setSuccess(false);
        }
    }

    /**
     * 账单支付（合单）
     * @param ids
     * @param openid
     * @return
     * @throws Exception
     */
    @RequestMapping("/multipleUnionPay")
    @UonePermissions
    public RestResponse multipleUnionPay(@RequestParam List<String> ids,BigDecimal allAmounts,@CacheParam String openid){
        ResSourceEntity sourceEntity = sourceService.getById(bilOrderService.getById(ids.get(0)).getSourceId());
        String orderCode = resProjectService.getCombinedOrderNumber(sourceEntity.getProjectId());
        for (String id:ids) {
            BilOrderEntity order = bilOrderService.getById(id);
            if("10".equals(order.getPayState())){
                order.setMergeCode(orderCode);
                bilOrderService.updateById(order);
            }
        }
        log.info("1.账单编号：" + orderCode + "用户发起支付！");
        RestResponse response = new RestResponse();
        String resp = null;
        Map<String,String> map = new HashMap<>();
        try {
            map = unionPayFegin.orderPay(getAmount(allAmounts),orderCode,openid);
            resp = map.get("resStr");
            System.out.println("返回结果:\n" + resp);
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject respJson = JSONObject.fromObject(resp);
        if (respJson.containsKey("targetStatus")) {
            String status = respJson.getString("targetStatus");
            bilInterfaceMsgService.addInterfaceMsg(map.get("requestMessage"), respJson.toString(), null,orderCode, "发起银联支付");
            if ("SUCCESS|SUCCESS".equals(status)) {
                String miniPayRequest = respJson.getString("miniPayRequest");
                response.setData(miniPayRequest);
                response.setSuccess(true);
                response.code(200);
                return response;
            } else {
                return response.setMessage("查询银联支付结果状态:" + status).setSuccess(false);
            }
        } else {
            log.info("银联返回消息：" + respJson);
            return response.setMessage("银联返回消息：" + respJson.getString("errMsg")).setSuccess(false);
        }
    }


    //BigDecimal转Unionpay string
    private String getAmount(BigDecimal payment) {
        return SafeCompute.multiply(payment, new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP).toString();
    }

}
