package cn.uone.business.cont.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.contract.ContractTypeEnum;
import cn.uone.application.enumerate.contract.PayTypeEnum;
import cn.uone.application.enumerate.contract.SettleStateEnum;
import cn.uone.application.enumerate.source.SourceStateEnum;
import cn.uone.bean.entity.business.biz.BizReleaseEntity;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.cont.ContContractSourceRelEntity;
import cn.uone.bean.entity.business.cont.ContRentLadderEntity;
import cn.uone.bean.entity.business.cont.bo.ContContractSourceRelBo;
import cn.uone.bean.entity.business.cont.vo.ContContractSourceRelVo;
import cn.uone.bean.entity.business.res.ResSourceConfigureEntity;
import cn.uone.bean.entity.business.res.ResSourceEntity;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.biz.service.IBizReleaseService;
import cn.uone.business.cont.dao.ContContractSourceRelDao;
import cn.uone.business.cont.service.IContContractSourceRelService;
import cn.uone.business.cont.service.IContRentLadderService;
import cn.uone.business.cont.service.IZzctContContractSourceRelService;
import cn.uone.business.res.service.IResProjectInfoService;
import cn.uone.business.res.service.IResSourceConfigureService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.res.service.impl.ResProjectInfoServiceImpl;
import cn.uone.web.util.SafeCompute;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * 服务实现类
 * 合同房源关系
 * 适用于漳州城投项目
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 * caizhanghe edit 2024-5-22
 */
@Service
public class ZzctContContractSourceRelServiceImpl extends ServiceImpl<ContContractSourceRelDao, ContContractSourceRelEntity> implements IZzctContContractSourceRelService {

    private static final Logger log = LoggerFactory.getLogger(ZzctContContractSourceRelServiceImpl.class);

    @Autowired
    private IResProjectInfoService projectInfoService;
    @Autowired
    private IResSourceConfigureService resSourceConfigureService;
    @Autowired
    IContRentLadderService contRentLadderService;
    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    @Lazy
    private IBilOrderService bilOrderService;
    @Autowired
    @Lazy
    private IBizReleaseService releaseService;

    @Override
    public List<ContContractSourceRelEntity> selectListByCondition(Map<String, Object> map) {
        return baseMapper.selectListByCondition(map);
    }

    /**
     * 参数判断;
     * 新增或者修改合同房源关系;
     */
    @Transactional
    @Override
    public ContContractSourceRelEntity addOrUpdateContMultiSourceRel(ContContractEntity contract, String sourceIds, BigDecimal price, BigDecimal cashPledge, BigDecimal subsidySum, List<ContRentLadderEntity> rentLadders) throws Exception {
        String contractId = contract.getId();//获取合同id
        Map<String,Object> map = new HashMap<>();
        map.put("contractId",contractId);
        ContContractSourceRelEntity rel = null;
        List<ContContractSourceRelEntity> relList = this.selectListByCondition(map);//查询是否有合同房源关系记录
        if(relList.size()>0){//多房源合同时查询出来会有多条相同的记录，取其中一条
            rel = relList.get(0);
        }

        // 人才押金=未扣减人才补贴的租金
        cashPledge = ObjectUtil.isNotNull(cashPledge) ? cashPledge : price.add(subsidySum);
        if (Arrays.asList(PayTypeEnum.TWO_ONE.getValue(), PayTypeEnum.TWO_THREE.getValue()).contains(contract.getPayType())) {
            cashPledge = SafeCompute.multiply(cashPledge, new BigDecimal(2));
        }
        if(rel == null){//如果为空是新增,如果不为空则可能是修改
            rel = new ContContractSourceRelEntity();
        }
        //ContContractSourceRelEntity rel = new ContContractSourceRelEntity();
        rel.setContractId(contract.getId());
        rel.setSourceId(sourceIds);
        rel.setFloatPrice(price);
        //rel.setFloatSize(priceVo.getFloatSize());
        rel.setSettleState(SettleStateEnum.SETTLE.getValue());
        rel.setOrigPrice(price);
        rel.setCashPledge(cashPledge);
        this.saveOrUpdate(rel);
        //3.租金阶梯
        /*if (CollectionUtils.isEmpty(rentLadders)) {
            contRentLadderService.saveContRentLadders(rel.getId(),price,contract);
        } else {
            for (ContRentLadderEntity rentLadder : rentLadders) {
                rentLadder.setStartDate(DateUtil.beginOfDay(rentLadder.getStartDate()));
                rentLadder.setEndDate(DateUtil.endOfDay(rentLadder.getEndDate()));
                rentLadder.setContractSourceId(rel.getId());
                contRentLadderService.save(rentLadder);
            }
        }*/
        return rel;
    }

}
