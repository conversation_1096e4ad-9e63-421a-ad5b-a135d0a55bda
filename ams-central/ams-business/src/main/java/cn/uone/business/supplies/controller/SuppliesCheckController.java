package cn.uone.business.supplies.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.fixed.FixedLeaseDetailsEntity;
import cn.uone.bean.entity.business.fixed.FixedLeaseEntity;
import cn.uone.bean.entity.business.supplies.CategoryEntity;
import cn.uone.bean.entity.business.supplies.SuppliesCheckEntity;
import cn.uone.bean.entity.business.supplies.SuppliesCheckSublistEntity;
import cn.uone.bean.entity.crm.UserEntity;
import cn.uone.business.supplies.service.ICategoryService;
import cn.uone.business.supplies.service.ISuppliesCheckService;
import cn.uone.business.supplies.service.ISuppliesCheckSublistService;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@RestController
@RequestMapping("/supplies/check")
public class SuppliesCheckController extends BaseController {
    @Autowired
    private ICategoryService categoryService;
    @Autowired
    private ISuppliesCheckService service;
    @Autowired
    private ISuppliesCheckSublistService sublistService;
    @Autowired
    private IUserFegin userFegin;


    @GetMapping("/page")
    public RestResponse page(Page<SuppliesCheckEntity> page, SuppliesCheckEntity entity){
        QueryWrapper<SuppliesCheckEntity> wrapper = new QueryWrapper<>();
        wrapper.orderByDesc("create_date");
        if(StrUtil.isNotBlank(entity.getCheckNumber())){
            wrapper.like("check_number","%"+entity.getCheckNumber()+"%");
        }
        if(StrUtil.isNotBlank(entity.getCheckBatch())){
            wrapper.like("check_batch","%"+entity.getCheckBatch()+"%");
        }
        if(StrUtil.isNotBlank(entity.getChecker())){
            wrapper.like("checker","%"+entity.getChecker()+"%");
        }
        if(StrUtil.isNotBlank(entity.getCheckState())){
            wrapper.eq("check_state",entity.getCheckState());
        }
        if(StrUtil.isNotBlank(entity.getAbnormalities())){
            wrapper.eq("abnormalities",entity.getAbnormalities());
        }
        IPage<SuppliesCheckEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }
    @PostMapping("/save")
    public RestResponse save(SuppliesCheckEntity entity){
        //主表
        entity.setCheckState("1");
        entity.insertOrUpdate();
        //子表
        for (String id:entity.getPropertyIds()) {
            CategoryEntity categoryEntity = categoryService.getById(id);
            SuppliesCheckSublistEntity sublistController = new SuppliesCheckSublistEntity();
            sublistController.setCheckId(entity.getId());
            sublistController.setAssertsId(id);
            sublistController.setStockQuantity(categoryEntity.getNowQuantity());
            sublistController.setName(categoryEntity.getName());
            sublistController.setType(categoryEntity.getType());
            sublistController.insert();
        }
        return RestResponse.success();
    }

    @GetMapping("/stockPage")
    public RestResponse stockPage(Page<CategoryEntity> page, CategoryEntity entity){
        if(entity.getPropertyIds()==null||entity.getPropertyIds().isEmpty()){
            return RestResponse.success();
        }
        QueryWrapper<CategoryEntity> wrapper = new QueryWrapper<>();
        wrapper.orderByDesc("create_date");
        wrapper.in("id",entity.getPropertyIds());
        IPage<CategoryEntity> p = categoryService.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

    @GetMapping("/addStockPage")
    public RestResponse addStockPage(Page<CategoryEntity> page, CategoryEntity entity){
        QueryWrapper<CategoryEntity> wrapper = new QueryWrapper<>();
        wrapper.orderByDesc("create_date");
        if(entity.getPropertyIds()!=null){
            wrapper.notIn("id",entity.getPropertyIds());
        }
        if(StrUtil.isNotBlank(entity.getName())){
            wrapper.like("name","%"+entity.getName()+"%");
        }
        if(StrUtil.isNotBlank(entity.getType())){
            wrapper.eq("type",entity.getType());
        }
        IPage<CategoryEntity> p = categoryService.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        service.removeByIds(ids);
        sublistService.delForCheckId(ids);
        return RestResponse.success();
    }
    /*
获取所有用户
*/
    @RequestMapping("/getAuditor")
    public RestResponse getAuditor() {
        List<UserEntity> userEntityList = userFegin.getUserList("");
        List<Map<String,String>> data=new ArrayList<>();
        Map<String,String> m=new HashMap<>();
        m.put("name", UoneSysUser.nickName());
        m.put("value",UoneSysUser.id());
        m.put("mobile",UoneSysUser.tel());
        data.add(m);
        for(UserEntity userEntity:userEntityList){
            if(!(userEntity.getId()).equals(UoneSysUser.id())){
                Map<String,String> map=new HashMap<>();
                map.put("name",userEntity.getRealName());
                map.put("value",userEntity.getId());
                map.put("mobile",userEntity.getTel());
                data.add(map);
            }
        }
        return RestResponse.success().setData(data);
    }
}
