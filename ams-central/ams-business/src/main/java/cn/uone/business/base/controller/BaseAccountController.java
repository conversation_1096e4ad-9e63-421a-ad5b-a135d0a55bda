package cn.uone.business.base.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.bean.entity.business.base.BaseAccountEntity;
import cn.uone.bean.entity.business.bil.BilTransferEntity;
import cn.uone.bean.entity.business.res.ResAccountSourceRelEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.business.base.service.IBaseAccountService;
import cn.uone.business.bil.service.IBilTransferService;
import cn.uone.business.res.service.IResAccountSourceRelService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.cache.util.CacheUtil;
import cn.uone.util.FileUtil;
import cn.uone.util.MinioUtil;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Api(value = "银行账号服务", tags = {"银行账号新增、修改等接口"})
@RestController
@RequestMapping("/base/account")
public class BaseAccountController extends BaseController {

    private static boolean isTest = true;
    @Autowired
    private IBaseAccountService baseAccountService;
    @Autowired
    private IBilTransferService bilTransferService;
    @Autowired
    private ISysFileService sysFileService;
    @Autowired
    private IResAccountSourceRelService resAccountSourceRelService;
    @Autowired
    private MinioUtil minioUtil;

    public static void main(String[] args) {
        System.out.println(RandomUtil.randomNumbers(6));
    }


    /**
     * 获取信息
     *
     * @param id
     * @return
     */
    @RequestMapping("/getInfo")
    public RestResponse getInfo(@RequestParam("id") String id) {
        RestResponse response = new RestResponse();
        try {
            response.setSuccess(true).setData(baseAccountService.getById(id));
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 分页查询
     *
     * @return
     */
    @RequestMapping("/getListForPage")
    public RestResponse getListForPage(Page page, String branchName, String code, String merchantId, String accountType) {
        RestResponse response = new RestResponse();
        return response.setSuccess(true).setData(baseAccountService.findByCondition(page, branchName, code, merchantId, accountType));
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    public RestResponse save(@RequestParam(required = false) List<MultipartFile> files, String filerImgs, @RequestParam(required = false) List<String> delImgs,
                             String identifyCode, @ModelAttribute BaseAccountEntity baseAccountEntity) {
        RestResponse response = new RestResponse();
        try {
            String validateCode = ObjectUtil.isNotNull(CacheUtil.get("validateCode")) ? CacheUtil.get("validateCode").toString() : "";
            if (isTest || validateCode.equals(identifyCode) && StrUtil.isNotEmpty(validateCode)) {
                baseAccountEntity.setProjectId(UoneHeaderUtil.getProjectId());
                baseAccountEntity.setState(ApprovalStateEnum.APPROVAL.getValue());
                baseAccountService.saveOrUpdate(baseAccountEntity);
                String id = baseAccountEntity.getId();
                if (ObjectUtil.isNotNull(files) && !files.isEmpty()) {
                    String[] filers = filerImgs.split(",");
                    saveFiles(id, files, filers);
                }
                if (ObjectUtil.isNotNull(delImgs) && !delImgs.isEmpty()) {
                    delFiles(delImgs);
                }
                response.setSuccess(true).setMessage("保存成功！");
            } else {
                response.setSuccess(false).setMessage("验证码错误！");
            }
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    private void delFiles(List<String> delImgs) {
        for (String img : delImgs) {
            SysFileEntity file = sysFileService.getById(img);
            //String isTrue = FileUtil.delete(file.getUrl());
            minioUtil.delete(file.getUrl());
            sysFileService.removeById(img);
        }
    }

    private void saveFiles(String id, List<MultipartFile> files, String[] filers) {
        for (MultipartFile file : files) {
            if (file.getSize() > 0l && !Arrays.asList(filers).contains(file.getOriginalFilename())) {
                //String url = FileUtil.save(file);
                String url = minioUtil.save(file);
                SysFileEntity entity = new SysFileEntity();
                entity.setFromId(id);
                entity.setType(SysFileTypeEnum.ACCOUNT.getValue());
                entity.setSize(file.getSize());
                entity.setName(file.getOriginalFilename());
                entity.setUrl(url);
                sysFileService.save(entity);
            }
        }
    }

    /**
     * 上传
     *
     * @return
     */
    @RequestMapping("/upload")
    public RestResponse upload(@RequestParam("file") MultipartFile file) {
        RestResponse response = new RestResponse();
        try {
            //String url = FileUtil.save(file);
            String url = minioUtil.save(file);
            response.setSuccess(true).setMessage("上传成功");
        } catch (Exception e) {
            response.setSuccess(false).setMessage("上传失败");
        }
        return response;
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public RestResponse delete(String identifyCode, String id) {
        RestResponse response = new RestResponse();
        try {
            String validateCode = ObjectUtil.isNotNull(CacheUtil.get("validateCode")) ? CacheUtil.get("validateCode").toString() : "";
            if (isTest || validateCode.equals(identifyCode) && StrUtil.isNotEmpty(identifyCode)) {
                QueryWrapper<BilTransferEntity> wrapper = new QueryWrapper();
                wrapper.eq("account_id", id);
                List<BilTransferEntity> bilTransferEntityList = bilTransferService.list(wrapper);
                if (!bilTransferEntityList.isEmpty()) {
                    response.setSuccess(false).setMessage("该账户存在转账记录无法删除！");
                    return response;
                }
                QueryWrapper<ResAccountSourceRelEntity> relWrapper = new QueryWrapper();
                relWrapper.eq("account_id", id);
                List<ResAccountSourceRelEntity> resAccountSourceRelEntityList = resAccountSourceRelService.list(relWrapper);
                if (!resAccountSourceRelEntityList.isEmpty()) {
                    response.setSuccess(false).setMessage("该账户已关联房源无法删除！");
                    return response;
                }
                baseAccountService.removeById(id);
                response.setSuccess(true).setMessage("删除成功！");
            } else {
                response.setSuccess(false).setMessage("验证码错误！");
            }
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 审核
     */
    @RequestMapping("/auditing")
    public RestResponse auditing(String identifyCode, @RequestParam List<String> ids) {
        RestResponse response = new RestResponse();
        try {
            String validateCode = ObjectUtil.isNotNull(CacheUtil.get("validateCode")) ? CacheUtil.get("validateCode").toString() : "";
            if (isTest || validateCode.equals(identifyCode) && StrUtil.isNotEmpty(identifyCode)) {
                for (String id : ids) {
                    BaseAccountEntity entity = baseAccountService.getById(id);
                    if (!ApprovalStateEnum.COMPLETE.getValue().equals(entity.getState())) {
                        entity.setState(ApprovalStateEnum.COMPLETE.getValue());
                        baseAccountService.updateById(entity);
                    }
                }
                response.setSuccess(true).setMessage("审核成功！");
            } else {
                response.setSuccess(false).setMessage("验证码错误！");
            }
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

}
