package cn.uone.business.bil.service;

import cn.uone.bean.entity.business.bil.BilInvestEntity;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.vo.BilInvestSearchVo;
import cn.uone.bean.entity.business.bil.vo.BilInvestVo;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-15
 */
public interface IBilInvestService extends IService<BilInvestEntity> {

    IPage<BilInvestVo> findByCondition(Page page, BilInvestSearchVo bilInvestSearchVo);

    RestResponse toShenHe(String id);

    BilInvestEntity getEntityByCode(String code);

    @Transactional
    void tradeSuccess(String orderCode, String sign, Map<String, String> params, String type) throws Exception;

    IPage<BilInvestVo> findBackendByCondition(Page page, BilInvestSearchVo bilInvestSearchVo);

    List<BilInvestEntity> getByIds(List<String> ids);

    List<BilInvestEntity> getInsertListByCode(String insertCode);

    BilInvestEntity getByCode(String code);

    List<BilInvestVo> bilExport(BilInvestSearchVo bilInvestSearchVo);

    List<BilInvestVo> getDiscountPayment(String payerId);

    void toChangeBalance();

    void toCreateAccount();

    BigDecimal getBalance(String contractId, String orderType, String balanceOrderType);
}
