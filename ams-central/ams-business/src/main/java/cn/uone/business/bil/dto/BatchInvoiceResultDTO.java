package cn.uone.business.bil.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 批量开票结果数据传输对象
 */
@Data
@ApiModel(description = "批量开票结果")
public class BatchInvoiceResultDTO {

    @ApiModelProperty(value = "成功列表")
    private List<SuccessItem> successList = new ArrayList<>();

    @ApiModelProperty(value = "失败列表")
    private List<FailureItem> failureList = new ArrayList<>();

    @ApiModelProperty(value = "总处理数量")
    private int total;

    @ApiModelProperty(value = "成功数量")
    private int successCount;

    @ApiModelProperty(value = "失败数量")
    private int failureCount;

    /**
     * 成功项
     */
    @Data
    @ApiModel(description = "开票成功项")
    public static class SuccessItem {
        @ApiModelProperty(value = "订单ID")
        private String orderId;

        @ApiModelProperty(value = "订单编号")
        private String orderCode;

        @ApiModelProperty(value = "发票号码")
        private String invoiceNumber;

        public SuccessItem() {}

        public SuccessItem(String orderId, String orderCode, String invoiceNumber) {
            this.orderId = orderId;
            this.orderCode = orderCode;
            this.invoiceNumber = invoiceNumber;
        }
    }

    /**
     * 失败项
     */
    @Data
    @ApiModel(description = "开票失败项")
    public static class FailureItem {
        @ApiModelProperty(value = "订单ID")
        private String orderId;

        @ApiModelProperty(value = "订单编号")
        private String orderCode;

        @ApiModelProperty(value = "失败原因")
        private String reason;

        public FailureItem() {}

        public FailureItem(String orderId, String orderCode, String reason) {
            this.orderId = orderId;
            this.orderCode = orderCode;
            this.reason = reason;
        }
    }

    /**
     * 更新统计数据
     */
    public void updateCounts() {
        this.total = this.successList.size() + this.failureList.size();
        this.successCount = this.successList.size();
        this.failureCount = this.failureList.size();
    }
} 