package cn.uone.business.cont.controller.yngy;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.ApprovalStateEnum;
import cn.uone.application.enumerate.RenterType;
import cn.uone.application.enumerate.SysFileTypeEnum;
import cn.uone.application.enumerate.contract.*;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.application.enumerate.source.SourceSignEnum;
import cn.uone.application.enumerate.source.SourceStateEnum;
import cn.uone.application.enumerate.source.SourceTypeEnum;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.bil.PriceStrategyEntity;
import cn.uone.bean.entity.business.cont.*;
import cn.uone.bean.entity.business.cont.vo.*;
import cn.uone.bean.entity.business.demo.DemoContractEntity;
import cn.uone.bean.entity.business.qys.QysContractEntity;
import cn.uone.bean.entity.business.res.*;
import cn.uone.bean.entity.business.sale.SaleCustomerEntity;
import cn.uone.bean.entity.business.sys.SysFileEntity;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.business.Guomi.service.IGuomiService;
import cn.uone.business.base.service.IBaseCarService;
import cn.uone.business.base.service.IBaseEnterpriseService;
import cn.uone.business.bil.service.IBilOrderAutoService;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.bil.service.IPriceStrategyService;
import cn.uone.business.bil.task.FixedOrderAutoTask;
import cn.uone.business.biz.service.IBizReleaseService;
import cn.uone.business.biz.service.IBizSettleService;
import cn.uone.business.cont.dao.ContContractSourceRelDao;
import cn.uone.business.cont.service.*;
import cn.uone.business.demo.service.IDemoContractService;
import cn.uone.business.dev.service.IDevDeviceLogService;
import cn.uone.business.dev.service.IDeviceSourceCheckService;
import cn.uone.business.flow.service.IActReDeploymentService;
import cn.uone.business.kingdee.service.impl.KingdeeApiServiceImpl;
import cn.uone.business.qys.service.IQysContractService;
import cn.uone.business.res.dao.ResSourceDao;
import cn.uone.business.res.service.*;
import cn.uone.business.sale.service.ISaleCustomerService;
import cn.uone.business.sys.service.ISysFileService;
import cn.uone.business.util.ContractUtil;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.crm.ISysMsgTemplateFegin;
import cn.uone.fegin.crm.ISysParaFegin;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.fegin.tpi.IQiyuesuoPrivateFegin;
import cn.uone.util.MinioUtil;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheLock;
import cn.uone.web.base.annotation.CacheParam;
import cn.uone.web.base.annotation.UoneLog;
import cn.uone.web.util.UoneHeaderUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Contract;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * 适用于岩内公寓需求
 * 前端控制器
 * 合同
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 * caizhanghe edit 2024-06-11
 */
@Api(value = "合同服务", tags = {"合同新增、修改等接口"})
@RestController
@RequestMapping("/yngy/cont/contract2")
public class YngyContContractController2 extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(YngyContContractController2.class);

    @Autowired
    private ISysFileService fileService;
    @Autowired
    private IResSourceService sourceService;
    @Autowired
    private IContContractService contractService;
    @Resource
    private IRenterFegin renterFegin;
    @Autowired
    private IResProjectInfoService projectInfoService;
    @Autowired
    private IContContractSourceRelService contContractSourceRelService;
    @Autowired
    private IContContractService contContractService;
    @Autowired
    IResSourceService resSourceService;
    @Autowired
    private IContTempService contTempService;
    @Autowired
    private IDemoContractService demoContractService;
    @Autowired
    ISysMsgTemplateFegin sysMsgTemplateFegin;
    @Autowired
    ISaleCustomerService saleCustomerService;
    @Autowired
    private IContCheckInHouseService checkInHouseService;
    @Autowired
    public IBizSettleService bizSettleService;
    @Autowired
    private IYngyContContractService yngyContContractService;
    @Autowired
    private IPriceStrategyService priceStrategyService;
    @Autowired
    private IBilOrderService bilOrderService;
    @Autowired
    private IActReDeploymentService actReDeploymentService;
    @Autowired
    private IQysContractService qysContractService;
    @Autowired
    private IQiyuesuoPrivateFegin qiyuesuoPrivateFegin;

    @PostMapping("/judgeStaff")
    public RestResponse judgeStaff(String phone,String sourceId,@RequestParam(required = false) String contTempId){
        try {
            RenterEntity renterEntity = renterFegin.getByTelAndType(phone, RenterType.COMMON.getValue());
            if(renterEntity == null){
                return RestResponse.success().setAny("platform", "0");
            }
            ResSourceEntity sourceEntity = resSourceService.getById(sourceId);
            String platform = PlatformEnum.YW.getValue();
            if(StrUtil.isNotBlank(contTempId)){
                ContTempEntity contTemp = contTempService.getById(contTempId);
                if(CustomerTypeEnum.STAFF.getValue().equals(contTemp.getCustomerType())){
                    platform = PlatformEnum.YG.getValue();
                }
            }
            return yngyContContractService.judgeStaff(sourceEntity,renterEntity,platform);
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
    }

    /**
     * 在线签约
     * 预览账单
     * @param request
     * @param isPreview
     * @param priceStrategyListStr
     * @return
     * @throws Exception
     */
    @PostMapping("/createBillPreview")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "createBillPreview", expire = 30)
    public RestResponse createBillPreview(HttpServletRequest request,
                                          boolean isPreview,String priceStrategyListStr) throws Exception {
        List<PriceStrategyEntity> priceStrategyList = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        if(priceStrategyListStr != null && !"".equals(priceStrategyListStr)){//json字符串转换为对象list
            PriceStrategyEntity[] priceStrategyList2 = mapper.readValue(priceStrategyListStr, new TypeReference<PriceStrategyEntity[]>() {});
            if(priceStrategyList2.length>0){
                for(PriceStrategyEntity priceStrategyEntity:priceStrategyList2){
                    PriceStrategyEntity priceStrategy = priceStrategyService.getById(priceStrategyEntity.getId());
                    priceStrategy.setSequ(priceStrategyEntity.getSequ());
                    priceStrategyList.add(priceStrategy);
                }
            }
        }
        boolean isManualAdd = false;//手动合同录入为false
        boolean isRecord = false;//合同备案暂时为false，可以从前端页面传进来
        return yngyContContractService.book(request,isPreview,priceStrategyList,isManualAdd,isRecord);
    }

    /**
     * 在线签约
     * 创建合同
     * @param request
     * @param isPreview
     * @param priceStrategyListStr
     * @return
     * @throws Exception
     */
    @PostMapping("/createContract")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "createContract", expire = 30)
    public RestResponse createContract(HttpServletRequest request,
                                       boolean isPreview,String priceStrategyListStr) throws Exception {
        List<PriceStrategyEntity> priceStrategyList = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        if(priceStrategyListStr != null && !"".equals(priceStrategyListStr)){//json字符串转换为对象list
            PriceStrategyEntity[] priceStrategyList2 = mapper.readValue(priceStrategyListStr, new TypeReference<PriceStrategyEntity[]>() {});
            if(priceStrategyList2.length>0){
                for(PriceStrategyEntity priceStrategyEntity:priceStrategyList2){
                    if(StrUtil.isBlank(priceStrategyEntity.getId())){
                        continue;
                    }
                    PriceStrategyEntity priceStrategy = priceStrategyService.getById(priceStrategyEntity.getId());
                    priceStrategy.setSequ(priceStrategyEntity.getSequ());
                    priceStrategyList.add(priceStrategy);
                }
            }
        }
        boolean isManualAdd = false;//手动合同录入为false
        boolean isRecord = false;//合同备案暂时为false，可以从前端页面传进来
        return yngyContContractService.book(request,isPreview,priceStrategyList,isManualAdd,isRecord);
    }

    @PostMapping("/continueCreateContract")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "continueCreateContract", expire = 30)
    public RestResponse continueCreateContract(HttpServletRequest request,
                                       boolean isPreview,String priceStrategyListStr) throws Exception {
        List<PriceStrategyEntity> priceStrategyList = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        if(priceStrategyListStr != null && !"".equals(priceStrategyListStr)){//json字符串转换为对象list
            PriceStrategyEntity[] priceStrategyList2 = mapper.readValue(priceStrategyListStr, new TypeReference<PriceStrategyEntity[]>() {});
            if(priceStrategyList2.length>0){
                for(PriceStrategyEntity priceStrategyEntity:priceStrategyList2){
                    PriceStrategyEntity priceStrategy = priceStrategyService.getById(priceStrategyEntity.getId());
                    priceStrategy.setSequ(priceStrategyEntity.getSequ());
                    priceStrategyList.add(priceStrategy);
                }
            }
        }
        boolean isManualAdd = false;//手动合同录入为false
        boolean isRecord = false;//合同备案暂时为false，可以从前端页面传进来
        return yngyContContractService.book(request,isPreview,priceStrategyList,isManualAdd,isRecord);
    }

    /**
     * 合同录入提交方法
     * 主要适用于岩内公寓项目
     * book方法中先生成租金账单预览,再根据租金账单预览生成租金账单
     * @param files
     * @param idFiles
     * @param xszFiles
     * @param jszFiles
     * @param tzrIDCardFiles
     * @param tzrTemporaryFiles
     * @param request
     * @param contractCode
     * @param isRecord
     * @return
     * @throws Exception
     * caizhanghe edit 2024-06-17
     */
    @PostMapping("/addContract")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "addContract", expire = 30)
    public RestResponse addContract(@RequestParam(required = false) List<MultipartFile> files,
                                    @RequestParam(required = false) List<MultipartFile> idFiles,
                                    @RequestParam(required = false) List<MultipartFile> xszFiles,
                                    @RequestParam(required = false) List<MultipartFile> jszFiles,
                                    @RequestParam(required = false) List<MultipartFile> tzrIDCardFiles,
                                    @RequestParam(required = false) List<MultipartFile> tzrTemporaryFiles,
                                    HttpServletRequest request,
                                    @CacheParam String contractCode,
                                    @RequestParam(required = false) String isRecord,boolean isPreview,String priceStrategyListStr) throws Exception {

        // 必填项判断
        if (StrUtil.isBlank(request.getParameter("tel"))) {
            return RestResponse.failure("手机号码不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("name"))) {
            return RestResponse.failure("租客姓名不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("idType"))) {
            return RestResponse.failure("身份证件类型不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("idNo"))) {
            return RestResponse.failure("身份证件号码不能为空");
        }
        if (StrUtil.isBlank(contractCode)) {
            return RestResponse.failure("合同编号不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("costConfigureId"))) {
            return RestResponse.failure("费用信息不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("payType"))) {
            return RestResponse.failure("缴费方式不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("shareStartTime"))) {
            return RestResponse.failure("租凭起日不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("shareEndTime"))) {
            return RestResponse.failure("租凭止日不能为空");
        }
        if (StrUtil.isBlank(request.getParameter("tempId"))) {
            return RestResponse.failure("合同模板不能为空");
        }
        /**纸质合同编号去重判断*/
        String paperCode = request.getParameter("paperCode");
        if(StrUtil.isNotBlank(paperCode)&&contContractService.repetitionByPaperCode(paperCode)>0){
            return RestResponse.failure("纸质合同编号不能重复");
        }
        String sourceId = request.getParameter("sourceId");
        ResSourceEntity source = sourceService.getById(sourceId);
        if (!BaseConstants.BOOLEAN_OF_TRUE.equals(source.getPublishTarget()) && (SourceTypeEnum.HOUSE.getValue().equals(source.getSourceType()))) {
            return RestResponse.failure(source.getCode() + "房源未发布");
        }
        if(!SourceStateEnum.UNRENT.getValue().equals(source.getState())){
            return RestResponse.failure("房源已被预定或出租");
        }
        if (SourceSignEnum.NOTRENT.getValue().equals(source.getSourceSign()) || SourceSignEnum.RESERVE.getValue().equals(source.getSourceSign())) {
            return RestResponse.failure(source.getCode() + "房源" + SourceSignEnum.getNameByValue(source.getSourceSign()) + ",不能签约");
        }
        if (SourceTypeEnum.HOUSE.getName().equals(request.getParameter("sourceType"))
                && PlatformEnum.YW.getValue().equals(request.getParameter("platform"))) {
            boolean isSyncCCB = contTempService.isSyncCCB(request.getParameter("tempId"));
            //判断，当同步参数和房源已上架再同时同步
            if (isSyncCCB && !BaseConstants.BOOLEAN_OF_TRUE.equals(source.getCcbPublishTarget())) {
                return RestResponse.failure("房源还未上架，请先上架再进行操作");
            }
        }
        QueryWrapper<DemoContractEntity> wrapper = new QueryWrapper<>();
        DemoContractEntity demoContractEntity = demoContractService.getOne(wrapper.eq("source_id",sourceId).last("limit 0,1"));
        if(null!=demoContractEntity&&DateUtil.beginOfDay(demoContractEntity.getEndTime()).getTime()<DateUtil.beginOfDay(DateUtil.parse(request.getParameter("shareEndTime"), "yyyy-MM-dd")).getTime()){
            return RestResponse.failure("合同截止日大于业主合同截止日，不允许签约");
        }
        String contractType = ContractTypeEnum.APARTMENT.getValue();
        // 商业/公区
        List<ContRentLadderEntity> rentLadderEntityList = null;
        ContTempEntity temp = contTempService.getById(request.getParameter("tempId"));
        if (null == temp) {
            return RestResponse.failure(source.getCode() + "未配置合同模版！");
        }
        // 参数判断
        if (StrUtil.isBlank(request.getParameter("sourceId")) || StrUtil.isBlank(request.getParameter("sourceType"))) {
            return RestResponse.failure("参数异常");
        }
        // 判断租赁周期是否符合项目租赁周期最小值
        ResProjectInfoEntity projectInfoEntity = projectInfoService.getOne(new QueryWrapper<ResProjectInfoEntity>().eq("project_id", UoneHeaderUtil.getProjectId())); // 获取项目信息
        if (null != projectInfoEntity && null != projectInfoEntity.getMinRentPeriod()) {
            if (ContractUtil.period(DateUtil.parse(request.getParameter("shareStartTime"), "yyyy-MM-dd"), DateUtil.parse(request.getParameter("shareEndTime"), "yyyy-MM-dd"), projectInfoEntity.getMinRentPeriod())) {
                return RestResponse.failure("租赁周期小于项目租赁周期最小值");
            }
        }
        String tel = request.getParameter("tel");
        RenterEntity renter = renterFegin.getByTelAndType(tel, RenterType.COMMON.getValue());
        // 判断租客用户是否存在,不存在则新增租客用户
        renter = contractService.judgeRenter2(tel, renter, request.getParameter("name"), request.getParameter("idType"), request.getParameter("idNo"), request.getParameter("email"), request.getParameter("address"));
        String payType = request.getParameter("payType");
        // 新增合同
        /*ContContractEntity contract = new ContContractEntity();
        contract.setContractType(contractType)
                .setContractTempletId(temp.getId())
                .setContractCode(request.getParameter("contractCode"))
                .setSignerId(renter.getId())
                .setStartDate(DateUtil.beginOfDay(DateUtil.parse(request.getParameter("startDate"), "yyyy-MM-dd")))
                .setEndDate(DateUtil.endOfDay(DateUtil.parse(request.getParameter("endDate"), "yyyy-MM-dd")))
                .setSignType(SignTypeEnum.ON_LINE.getValue().equals(request.getParameter("signType")) ? SignTypeEnum.ON_LINE.getValue() : SignTypeEnum.OFF_LINE.getValue()) // 线下签约
                .setPayType(payType)
                .setCostConfigureId(request.getParameter("costConfigureId"))
                .setPaperCode(request.getParameter("paperCode"))
                .setIsOrganize(BaseConstants.BOOLEAN_OF_FALSE)
                .setSignDate(new Date());
        long diffDay = DateUtil.betweenDay(contract.getStartDate(), new Date(), false);
        if (diffDay >= 0) {
            contract.setState(ContractStateEnum.STATUS_TAKE_EFFECT.getValue());
        } else {
            contract.setState(ContractStateEnum.STATUS_NO_IN_TIME.getValue());
        }
        contract.setManager(UoneSysUser.id());*/
        //如果有传值，说明是公寓合同且可能为第三方合同
        /*if (StrUtil.isNotBlank(request.getParameter("platform"))) {
            contract.setPlatform(request.getParameter("platform"));
            if (!PlatformEnum.YW.getValue().equals(request.getParameter("platform"))) {
                contract.setPlatformCode(request.getParameter("paperCode"))
                        .setPaperCode(null);
            }
        }*/
        // 判断是否重复生成合同
        /*if (contractService.getOne(new QueryWrapper<ContContractEntity>().eq("contract_code", contract.getContractCode())) != null) {
            return RestResponse.failure("该合同编号已经存在,不能签约！");
        }*/
        /*contractService.save(contract);*/
        // 新增合同信息
        /*ContContractInfoEntity contractInfo = contractInfoService.handleContractInfo(new ContContractInfoEntity(), contract.getId(), null, renter, temp, source.getProjectId());
        contractInfoService.save(contractInfo);*/
        List<PriceStrategyEntity> priceStrategyList = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        if(priceStrategyListStr != null && !"".equals(priceStrategyListStr)){//json字符串转换为对象list
            PriceStrategyEntity[] priceStrategyList2 = mapper.readValue(priceStrategyListStr, new TypeReference<PriceStrategyEntity[]>() {});
            if(priceStrategyList2.length>0){
                for(PriceStrategyEntity priceStrategyEntity:priceStrategyList2){
                    PriceStrategyEntity priceStrategy = priceStrategyService.getById(priceStrategyEntity.getId());
                    priceStrategy.setSequ(priceStrategyEntity.getSequ());
                    priceStrategyList.add(priceStrategy);
                }
            }
        }
        boolean isManualAdd = true;//手动合同录入为true
        //调用公共方法，先生成租金账单预览，再根据租金账单预览生成租金账单
        RestResponse res = yngyContContractService.book(request,isPreview,priceStrategyList,isManualAdd,BaseConstants.BOOLEAN_OF_TRUE.equals(isRecord));
        ContContractEntity contract = (ContContractEntity) res.get("contract");//获取生成的合同
        ContContractSourceRelEntity contractSourceRelEntity = contContractSourceRelService.getByContractIdAndSourceId(contract.getId(),sourceId);//根据合同id获取book方法中已经生成合同房源关系记录
        // 新增合同房源关系
        BigDecimal price = null;
        BigDecimal deposit = null;
        if (StrUtil.isNotBlank(request.getParameter("price"))) {
            price = new BigDecimal(request.getParameter("price"));
        }
        if (StrUtil.isNotBlank(request.getParameter("deposit"))) {
            deposit = new BigDecimal(request.getParameter("deposit"));
        }
        //ContContractSourceRelEntity contractSourceRelEntity = contractSourceRelService.addContContractSourceRel(contract, source, price, deposit, temp.getSubsidyPrice(), rentLadderEntityList);
        int idnum = 0;
        int tenum = 0;
        if (StrUtil.isNotEmpty(request.getParameter("contCheckInUserList"))) {
            List<ContCheckInUserVo> checkInUserList = JSONArray.parseArray(request.getParameter("contCheckInUserList"), ContCheckInUserVo.class);
            // 判断同住人用户是否存在,不存在则新增同住人用户
            contractService.saveCheckInUser(tzrIDCardFiles, tzrTemporaryFiles, checkInUserList, sourceId, tel, contractSourceRelEntity, idnum, tenum, ApprovalStateEnum.APPROVAL.getValue());
        }
        fileService.uploadFiles(idFiles, contract.getId(), "上传身份证照片异常", SysFileTypeEnum.ID_CARD);
        boolean createPdf = false;
        // 判断是否生成PDF
        if (!ContractTypeEnum.APARTMENT.getValue().equals(contract.getContractType())) {
            if (StrUtil.isBlank(request.getParameter("isPdf")) || BaseConstants.BOOLEAN_OF_TRUE.equals(request.getParameter("isPdf"))) {
                createPdf = true;
                contTempService.createSignPdf(contract.getId());
            }
        }/* else {
            createPdf = true;
            contTempService.createSignPdf(contract.getId());
        }*/
        //签字/未签字pdf 商业合同详情页面
        if (createPdf && ContractTypeEnum.BUSINESS.getValue().equals(contract.getContractType()) && SignTypeEnum.OFF_LINE.getValue().equals(contract.getSignType())) {
            List<SysFileEntity> contractFiles = fileService.getListByFromIdAndType(contract.getId(), SysFileTypeEnum.CONTRACT);
            if (!CollectionUtils.isEmpty(contractFiles)) {
                for (SysFileEntity file : contractFiles) {
                    file.setType(SysFileTypeEnum.UNSIGNED_CONTRACT.getValue());
                }
                fileService.updateBatchById(contractFiles);
            }
        }
        // 判断是否存在附件，是则新增合同;
        if (StrUtil.isNotBlank(request.getParameter("fileName"))) {
            SysFileEntity entity = new SysFileEntity();
            entity.setFromId(contract.getId());
            entity.setType(SysFileTypeEnum.CONTRACT.getValue());
            entity.setName(request.getParameter("contractFile"));
            entity.setUrl(request.getParameter("fileName"));
            fileService.save(entity);
        }
        if (ContractTypeEnum.BUSINESS.getValue().equals(contract.getContractType()) && SignTypeEnum.ON_LINE.getValue().equals(contract.getSignType())) {
            contract.setState(ContractStateEnum.STATUS_SIGNING.getValue()).updateById();
            source.setState(SourceStateEnum.BOOKED.getValue()).updateById();
        } else {
            //contContractService.auditContract(contract, source, renter, BaseConstants.BOOLEAN_OF_TRUE.equals(isRecord));
            //直接办理入住
            checkInHouseService.directChenkIn(contract.getId());
        }
        return RestResponse.success("签约成功");
    }

    /**
     * 意向签约
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping("/intentContract")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "intentContract", expire = 30)
    public RestResponse intentContract(HttpServletRequest request) throws Exception {
        return yngyContContractService.intent(request);
    }

    /**
     * 意向金罚没
     * @return
     */
    @PostMapping("/intentConfiscate")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "intentConfiscate", expire = 30)
    public RestResponse intentConfiscate(){
        try {
            yngyContContractService.confiscateIntention();
        } catch (BusinessException e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
        return RestResponse.success();
    }

    @PostMapping("/cancelContract")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "cancelContract", expire = 30)
    @UoneLog("取消合同或意向书")
    public RestResponse cancelContract(String id, String remark){
        try {
            if (StrUtil.isEmpty(remark)) {
                return RestResponse.failure("取消原因不能为空");
            }
            yngyContContractService.cancelContract(id,remark);
        } catch (BusinessException e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
        return RestResponse.success();
    }

    @PostMapping("/cancelContractByCustomerId")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "cancelContractByCustomerId", expire = 30)
    @UoneLog("根据意向登记取消意向签约或正式签约")
    public RestResponse cancelContractByCustomerId(String id, String remark){
        try {
            SaleCustomerEntity customer = saleCustomerService.getById(id);
            String renterId = customer.getRenterId();
            String sourceId = customer.getSourceId();
            QueryWrapper query = new QueryWrapper<>();
            query.eq("payer_id", renterId);
            query.eq("source_id", sourceId);
            query.eq("order_type", OrderTypeEnum.DEPOSIT.getValue());
            query.notIn("pay_state", PayStateEnum.CANCEL.getValue(),PayStateEnum.REFUNDED.getValue());
            query.orderByDesc("create_date");
            BilOrderEntity yxOrder = null;
            List<BilOrderEntity> yxOrderList = bilOrderService.list(query);
            if(yxOrderList !=null && yxOrderList.size()>0){
                yxOrder = yxOrderList.get(0);
            }
            String contractId = CustomerStateEnum.INTENTION.getValue().equals(customer.getState())?yxOrder.getCcbBillId():yxOrder.getContractId();
            yngyContContractService.cancelContract(contractId,remark);
        } catch (BusinessException e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
        return RestResponse.success();
    }

    /**
     * 发起意向金退款审批
     * @param id
     * @return
     */
    @PostMapping("/toRefundAudit")
    @Transactional(rollbackFor = Exception.class)
    @CacheLock(prefix = "toRefundAudit", expire = 30)
    @UoneLog("发起意向金退款审批")
    public RestResponse toRefundAudit(String id){
        BilOrderEntity yxOrder = bilOrderService.getById(id);
        yxOrder.setPayState(PayStateEnum.REFUNDAUDITING.getValue());
        bilOrderService.updateById(yxOrder);
        ResSourceEntity source = resSourceService.getById(yxOrder.getSourceId());
        //发起新签约审批流程
        actReDeploymentService.intentRefundAuditStart(id,source.getProjectId());
        return RestResponse.success();
    }

    //审核不通过
    @RequestMapping("/toNotAudit")
    @UoneLog("合同审核不通过")
    @Transactional
    public RestResponse toNotAudit(String id, String signType) throws Exception {
       //修改合同状态
        ContContractEntity entity = contractService.getById(id);
        if (!ContractStateEnum.STATUS_REVIEW.getValue().equals(entity.getState())) {
            return RestResponse.failure("该合同不是待审核状态");
        }
        entity.setState(ContractStateEnum.STATUS_REJECT.getValue());//10为已驳回状态
        entity.insertOrUpdate();
        //RestResponse resp = iFadadaFegin.cancelContract(entity.getContractCode());
        QueryWrapper query = new QueryWrapper();
        query.eq("contract_id",id);
        query.eq("sign_type",signType);
        query.eq("state","3");
        QysContractEntity qysContractEntity = qysContractService.getOne(query);
        RestResponse resp = qiyuesuoPrivateFegin.cancelContract(qysContractEntity.getQysContractId(),"审核不通过");
        qysContractEntity.setState("4");
        qysContractService.updateById(qysContractEntity);
        if (!"200".equals(resp.get("code").toString())) {
            return resp;
        }
        //QueryWrapper wrapper = new QueryWrapper();
        //wrapper.eq("from_id", entity.getId());
        //wrapper.eq("type", SysFileTypeEnum.CONTRACT.getValue());
        //sysFileService.remove(wrapper);
        return RestResponse.success("审核不通过");
    }

}
