package cn.uone.business.bil.service;

import cn.uone.bean.entity.business.bil.EquipmentWarnEntity;
import cn.uone.bean.entity.business.bil.vo.EquipmentWarnVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-03
 */
public interface IEquipmentWarnService extends IService<EquipmentWarnEntity> {

    IPage<EquipmentWarnVo> findByCondition(Page page, EquipmentWarnVo equipmentWarnVo);

    EquipmentWarnEntity getEntityByUUid(String uuid);
}
