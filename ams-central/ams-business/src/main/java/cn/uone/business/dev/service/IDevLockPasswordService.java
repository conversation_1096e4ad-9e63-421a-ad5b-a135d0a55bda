package cn.uone.business.dev.service;

import cn.uone.bean.entity.business.dev.DevLockPasswordEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 门锁密码表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-30
 */
public interface IDevLockPasswordService extends IService<DevLockPasswordEntity> {
    //查出租客在该门锁的授权记录(锁有效时间为空的数据在前面)
    DevLockPasswordEntity queryNullFirst(String deviceId, String renterId);

    List<DevLockPasswordEntity> getRecords(String deviceId, String beginDate, String endDate);
}
