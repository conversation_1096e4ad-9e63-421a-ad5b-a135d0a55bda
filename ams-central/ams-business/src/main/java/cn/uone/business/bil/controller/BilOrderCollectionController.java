package cn.uone.business.bil.controller;


import cn.uone.bean.entity.business.afforest.AreaEntity;
import cn.uone.bean.entity.business.bil.BilOrderCollectionEntity;
import cn.uone.bean.entity.business.bil.BilOrderConfirmEntity;
import cn.uone.business.bil.service.IBilOrderCollectionService;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.UoneLog;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import cn.uone.web.base.BaseController;

/**
 * <p>
 * 催收数据表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@RestController
@RequestMapping("/bil/collection")
public class BilOrderCollectionController extends BaseController {

    @Autowired
    private IBilOrderCollectionService service;


    @RequestMapping("/getByOrderId")
    @UoneLog("获取账单数据")
    public RestResponse getByOrderId(String orderId){
        QueryWrapper<BilOrderCollectionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_id",orderId);
        BilOrderCollectionEntity collectionEntity = service.getOne(queryWrapper);
        return RestResponse.success().setData(collectionEntity);
    }

    @PostMapping("/save")
    @UoneLog("维护账单数据")
    public RestResponse save(BilOrderCollectionEntity entity){
        entity.insertOrUpdate();
        return RestResponse.success();
    }

}
