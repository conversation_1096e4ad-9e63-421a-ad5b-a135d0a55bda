package cn.uone.business.xhcosmic.service;

import cn.uone.bean.entity.business.cosmic.vo.CosmicTransactionVo;
import cn.uone.bean.entity.business.xhcosmic.CosmicTransactionEntity;
import cn.uone.bean.entity.tpi.cosmic.transaction.TransactionCreditedQueryDto;
import cn.uone.web.base.BusinessException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 金蝶(星瀚)交易明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
public interface ICosmicTransactionService extends IService<CosmicTransactionEntity> {

    /**
     * 批量不重复保存
     *
     * @param entities
     * @return
     */
    List<CosmicTransactionEntity> insertBatch(List<CosmicTransactionEntity> entities);

    /**
     * 金蝶交易明细 待认领查询
     *
     * @throws Exception
     */
    List<CosmicTransactionEntity> cosmicQuery(LocalDate bizDate, List<String> companyNumber, String bankAccountNumber, String acctPropertyNumber,
                             BigDecimal creditAmount, BigDecimal noClaimAmount, LocalDateTime modifyTime,
                             Boolean isFullQuery, Integer pageNo, Integer pageSize) throws Exception;

    /**
     * 金蝶交易明细 待认领查询
     *
     * @throws Exception
     */
    List<CosmicTransactionEntity> cosmicQuery(TransactionCreditedQueryDto dto) throws Exception;

    /**
     * 自动认领交易明细 -> 找到对应的应收单(one or more)->获取应收单的单号生成收款处理单
     *
     * @param entity
     */
    void autoClaimTransactionDetail(List<CosmicTransactionEntity> entity);

    void autoClaimTransactionDetailByBankId(List<String> bankIds);

    /**
     * 交易明细单分页查询
     *
     * @param page
     * @param cosmicTransactionVo
     * @return 明细地址
     * @throws BusinessException
     */
    IPage<CosmicTransactionEntity> queryPage(Page<CosmicTransactionEntity> page, CosmicTransactionVo cosmicTransactionVo) throws BusinessException;

    /**
     * 前端页面手动认领
     *
     * @param id        手动认领
     * @param incomeIds 应付单ids
     */
    void claimTransaction(String id, Collection<String> incomeIds, CosmicTransactionEntity matchedTransaction) throws BusinessException;
    
    /**
     * 判断交易明细是否已经认领完成
     * @param transactionId 交易明细ID
     * @return 是否认领完成
     */
    boolean isTransactionFullyClaimed(String transactionId);
    
    /**
     * 获取交易明细的剩余可认领金额
     * @param transactionId 交易明细ID
     * @return 剩余可认领金额
     */
    BigDecimal getRemainingClaimableAmount(String transactionId);
    
    /**
     * 批量处理指定日期范围内的交易明细自动认领
     * 该方法会查询指定日期范围内的交易明细，然后调用autoClaimTransactionDetail方法进行认领
     * 
     * @param startDate 开始日期（包含）
     * @param endDate 结束日期（包含）
     * @return 处理的交易明细数量
     * @throws Exception 处理过程中可能出现的异常
     */
    int batchAutoClaimByDateRange(LocalDate startDate, LocalDate endDate) throws Exception;
}
