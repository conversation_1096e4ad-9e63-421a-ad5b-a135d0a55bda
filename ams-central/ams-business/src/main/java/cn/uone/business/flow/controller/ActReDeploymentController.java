package cn.uone.business.flow.controller;


import cn.uone.bean.entity.business.fixed.FixedRfidEntity;
import cn.uone.business.fixed.service.IFixedRfidService;
import cn.uone.business.flow.service.IFlowDefinitionService;
import cn.uone.shiro.bean.LoginType;
import cn.uone.shiro.bean.UonePermissions;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.uone.web.base.BaseController;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@RestController
@RequestMapping("/act-re-deployment-entity")
@UonePermissions(LoginType.CUSTOM)
public class ActReDeploymentController extends BaseController {
    @Autowired
    private IFlowDefinitionService flowDefinitionService;

    @ApiOperation(value = "读取流程图片文件")
    @GetMapping("/readImage")
    public void readImage(@ApiParam(value = "流程定义id") @Param(value = "deployId") String deployId, HttpServletResponse response) {
        OutputStream os = null;
        BufferedImage image = null;
        try {
            image = ImageIO.read(flowDefinitionService.readImage(deployId));
            response.setContentType("image/png");
            os = response.getOutputStream();
            if (image != null) {
                ImageIO.write(image, "png", os);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null) {
                    os.flush();
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

}
