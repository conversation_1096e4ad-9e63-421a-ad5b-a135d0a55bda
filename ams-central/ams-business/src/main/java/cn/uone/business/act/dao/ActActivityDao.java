package cn.uone.business.act.dao;

import cn.uone.bean.entity.business.act.ActActivityEntity;
import cn.uone.bean.entity.business.act.ActSearchVo;
import cn.uone.bean.entity.business.act.ActVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 * 活动表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
public interface ActActivityDao extends BaseMapper<ActActivityEntity> {
    IPage<ActVo> listPage(Page page, ActSearchVo vo);
}
