package cn.uone.business.rpt.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.application.enumerate.order.PayStateEnum;
import cn.uone.bean.entity.business.report.vo.ConfirmRefundVo;
import cn.uone.bean.parameter.ConfirmRefundPo;
import cn.uone.business.rpt.service.IReportConfirmRefundService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.ExcelRender;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 确认退款明细表
 */
@RestController
@RequestMapping("/report/confirmRefund")
public class ReportConfirmRefundController extends BaseController {

    @Autowired
    private IReportConfirmRefundService confirmRefundService;


    @RequestMapping(value = "/page", method = RequestMethod.GET)
    public RestResponse page(Page page, ConfirmRefundPo param) {
        param.setProjectId(UoneHeaderUtil.getProjectId());
        IPage record=confirmRefundService.report(page, param);
        setData(record.getRecords(),false);
        return RestResponse.success().setData(record);
    }

    @RequestMapping("/export")
    public void sourceReportExport(HttpServletResponse response, ConfirmRefundPo param) throws BusinessException {
        Map<String, Object> beans = Maps.newHashMap();
        param.setProjectId(UoneHeaderUtil.getProjectId());
        List<ConfirmRefundVo> list=confirmRefundService.report(param);
        setData(list,true);
        beans.put("confirm", list);
        ExcelRender.me("/excel/export/confirmRefund.xlsx").beans(beans).render(response);
    }

    public void setData(List<ConfirmRefundVo> records,boolean export){
        BigDecimal zero=new BigDecimal(0);
        BigDecimal fone=new BigDecimal(-1);
        for(ConfirmRefundVo vo:records){
            if(vo.getCqzjSj().compareTo(zero)==0){
                vo.setCqzjBjTime("");
            }
            if(PayStateEnum.REFUNDPENDING.getValue().equals(vo.getPayState())){
                vo.setStzj(zero);
                vo.setStyj(zero);
                vo.setSjfmyj(zero);
                vo.setStshf(zero);
            }else{
                if(OrderTypeEnum.HUANFANGFEE.getValue().equals(vo.getOrderType())){//換房費
                    BigDecimal zj=vo.getYtzj().multiply(fone).subtract(ObjectUtil.isNull(vo.getHff())?zero:vo.getHff()).subtract(vo.getHj().compareTo(zero)==-1?zero:vo.getHj());
                    if(zj.compareTo(zero)==1){
                        vo.setStzj(zj.multiply(fone));
                    }else{
                        vo.setStzj(zero);
                    }
                    if(vo.getYtzj().compareTo(zero)==1 || vo.getYtzj().compareTo(zero)==0){
                        BigDecimal cqzj=zero;
                        if(ObjectUtil.isNotNull(vo.getCqzj())){
                            cqzj=vo.getCqzj();
                        }
                        BigDecimal yj=vo.getYtyj().subtract(ObjectUtil.isNull(vo.getHff())?zero:vo.getHff()).subtract(vo.getHj().compareTo(zero)==-1?zero:vo.getHj()).subtract(cqzj);
                        vo.setStyj(yj.compareTo(zero)==1?yj:zero);
                    }else{
                        BigDecimal sum=vo.getHj().compareTo(zero)==-1?zero:vo.getHj().add(ObjectUtil.isNull(vo.getHff())?zero:vo.getHff());
                        BigDecimal dk=vo.getYtzj().multiply(fone).subtract(sum);
                        if(dk.compareTo(zero)==1 || dk.compareTo(zero)==0){
                            vo.setStyj(vo.getYtyj());
                        }else{
                            BigDecimal yj=vo.getYtyj().subtract(dk.multiply(fone));
                            vo.setStyj(yj.compareTo(zero)==1?yj:zero);
                        }
                    }
                    vo.setSjfmyj(zero);
                }else if(OrderTypeEnum.CHECKOUTTUI.getValue().equals(vo.getOrderType())){//退房退款
                    BigDecimal hff=zero;
                    if(ObjectUtil.isNotNull(vo.getHff())){
                        hff=vo.getHff();
                    }
                    BigDecimal zj=vo.getYtzj().multiply(fone).subtract(vo.getHj().compareTo(zero)==-1?zero:vo.getHj()).subtract(hff);
                    if(zj.compareTo(zero)==1){
                        vo.setStzj(zj.multiply(fone));
                    }else{
                        vo.setStzj(zero);
                    }

                    if(vo.getFmyj().compareTo(zero) > 0){
                        vo.setStyj(zero);
                        if(vo.getYtzj().compareTo(zero)>=0){
                            BigDecimal zz=vo.getFmyj().subtract(vo.getHj().compareTo(zero)==-1?zero:vo.getHj());
                            vo.setSjfmyj(zz.compareTo(zero)==1?zz:zero);
                        }else{
                            BigDecimal hj=vo.getYtzj().multiply(fone).subtract(vo.getHj().compareTo(zero)==-1?zero:vo.getHj());

                            if(hj.compareTo(zero)==-1){
                                vo.setSjfmyj(vo.getFmyj().add(hj).compareTo(zero)==1?vo.getFmyj().add(hj):zero);
                            }else{
                                vo.setSjfmyj(vo.getFmyj());
                            }
                        }
                    }else{
                        if(vo.getYtzj().compareTo(zero)>=0){
                            BigDecimal cqzj=zero;
                            hff=zero;
                            if(ObjectUtil.isNotNull(vo.getHff())){
                                hff=vo.getHff();
                            }
                            if(ObjectUtil.isNotNull(vo.getCqzj())){
                                cqzj=vo.getCqzj();
                            }

                            BigDecimal yj=vo.getYtyj().subtract(vo.getHj().compareTo(zero)==-1?zero:vo.getHj()).subtract(hff).subtract(cqzj);
                            vo.setStyj(yj.compareTo(zero)==1?yj:zero);
                        }else{
                            hff=zero;
                            if(ObjectUtil.isNotNull(vo.getHff())){
                                hff=vo.getHff();
                            }
                            BigDecimal dk=vo.getYtzj().multiply(fone).subtract(vo.getHj().compareTo(zero)==-1?zero:vo.getHj()).subtract(hff);
                            if(dk.compareTo(zero)==1 || dk.compareTo(zero)==0){
                                vo.setStyj(vo.getYtyj());
                            }else{
                                BigDecimal yj=vo.getYtyj().subtract(dk.multiply(fone));
                                vo.setStyj(yj.compareTo(zero)==1?yj:zero);
                            }
                        }
                        vo.setSjfmyj(zero);
                    }
                }else if(OrderTypeEnum.DEPOSITREFUND.getValue().equals(vo.getOrderType())){//定金退款
                    vo.setStyj(vo.getYtyj());
                    vo.setSjfmyj(zero);
                }
                if(!OrderTypeEnum.DEPOSITREFUND.getValue().equals(vo.getOrderType())){
                    if(vo.getYtyj().compareTo(zero)!=0){
                        vo.setYtyj(vo.getYtyj().multiply(fone));
                    }
                    if(vo.getStyj().compareTo(zero)!=0){
                        vo.setStyj(vo.getStyj().multiply(fone));
                    }
                    if(vo.getHj().compareTo(zero)==-1){
                        vo.setStshf(vo.getHj());
                    }else{
                        vo.setStshf(zero);
                    }
                }
            }


            if(export){
                vo.setOrderType(OrderTypeEnum.getNameByValue(vo.getOrderType()));
            }
        }
    }

}
