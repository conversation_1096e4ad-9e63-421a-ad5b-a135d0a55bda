package cn.uone.business.pbls.dao;

import cn.uone.bean.entity.business.pbls.ParkingApplyEntity;
import cn.uone.bean.entity.business.pbls.vo.ParkingApplyVo;
import cn.uone.mybatis.inerceptor.DataScope;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-22
 */
public interface ParkingApplyDao extends BaseMapper<ParkingApplyEntity> {

    IPage<ParkingApplyVo> selectParkingApplyByMap(Page page, @Param("map")Map<String, Object> map, DataScope dataScope);

    IPage<ParkingApplyVo> selectParkingRecordByMap(Page page, @Param("map")Map<String, Object> map, DataScope dataScope);


    ParkingApplyVo getApplyVo(@Param("map")Map<String, Object> map);


    /**
     * 本年的（每月）车位申请数量
     */
    Map<String,Object> getQuantityForMonth();

    int selectBySpaceId(String spaceId);

    int selectByLicense(String license);

    int selectByUserId(String userId);

    int countByStatus();

    ParkingApplyEntity getByLicense(String license);
}
