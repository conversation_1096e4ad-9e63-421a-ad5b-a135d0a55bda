package cn.uone.business.bil.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 批量开票请求数据传输对象
 */
@Data
@ApiModel(description = "批量开票请求")
public class BatchInvoiceRequestDTO {

    @ApiModelProperty(value = "订单ID列表", required = true)
    private List<String> orderIds;

    @ApiModelProperty(value = "开票类型代码：REPORT或BAIWANG", required = true)
    private String invoiceTypeCode;
} 