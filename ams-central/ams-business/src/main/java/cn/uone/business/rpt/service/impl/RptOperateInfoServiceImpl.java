package cn.uone.business.rpt.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.uone.application.enumerate.order.OrderTypeEnum;
import cn.uone.bean.entity.business.rpt.RptIncomeInfoEntity;
import cn.uone.bean.entity.business.rpt.RptOperateInfoEntity;
import cn.uone.business.rpt.dao.RptIncomeInfoDao;
import cn.uone.business.rpt.dao.RptOperateInfoDao;
import cn.uone.business.rpt.service.IRptOperateInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 运营详情-出租情况 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
@Service
public class RptOperateInfoServiceImpl extends ServiceImpl<RptOperateInfoDao, RptOperateInfoEntity> implements IRptOperateInfoService {

    @Resource
    private RptIncomeInfoDao incomeInfoDao;

    public List<RptIncomeInfoEntity> getIncomeInfos(String projectId, List<String> sourceType,String[] types,String feeName){
        List<RptIncomeInfoEntity> ls = incomeInfoDao.getLiveFeeIncomeInfo(projectId,sourceType, Arrays.asList(types));
        List<RptIncomeInfoEntity> cs = incomeInfoDao.getOutIncomeInfo(projectId,sourceType, Arrays.asList(types));
        List<RptIncomeInfoEntity> list = Lists.newArrayList();
        if(cs!=null){
            if(ls!=null)
            cs.addAll(ls);
            list= cs;
        }else if(ls!=null){
            list= ls;
        }
        List<RptIncomeInfoEntity> rlist = Lists.newArrayList();
        for (RptIncomeInfoEntity i:list) {
            i.setOrderType(feeName);
            boolean isAdd = true;
            for(RptIncomeInfoEntity ri:rlist){
                if(i.getSourceType().equals(ri.getSourceType())&&i.getProjectId().equals(ri.getProjectId())){
                    isAdd= false;
                    ri.add(i);
                }
            }
            if(isAdd){
                rlist.add(i);
            }
        }

        return rlist;
    }


    @Transactional
    public List<RptIncomeInfoEntity> batchIncomeInfo(){

        List<RptIncomeInfoEntity> list = Lists.newArrayList();
        List<RptIncomeInfoEntity> ys = incomeInfoDao.getYajinIncomeInfo(null,null);//押金
        List<RptIncomeInfoEntity> fs = incomeInfoDao.getFineIncomeInfo(null,null);//罚没
        List<RptIncomeInfoEntity> rs = incomeInfoDao.getRentIncomeInfo(null,null);//租金

        String[] types = {"240", "130", "210", "180", "190", "200", "220", "400", "410", "360"};
        List<RptIncomeInfoEntity> ls = getIncomeInfos(null,null,types,OrderTypeEnum.FIXED.getValue());

        String[] types1 = {"30","120","50","140","160","230"};
        List<RptIncomeInfoEntity> ls1 = getIncomeInfos(null,null,types1,OrderTypeEnum.ENERGY.getValue());

        String[] types2 = {"350","330","310","70","80","100","370","371"};
        List<RptIncomeInfoEntity> ls2 = getIncomeInfos(null,null,types2,OrderTypeEnum.QITAFEE.getValue());

        List<RptIncomeInfoEntity> ss = incomeInfoDao.getSubsidyIncomeInfo(null,null);//人才
        for (RptIncomeInfoEntity v:ys){
//            RptIncomeInfoEntity av = new RptIncomeInfoEntity();//总计
//            av.setSourceType(v.getSourceType());
//            av.setProjectId(v.getProjectId());
//            av.setOrderType("汇总");
            //获取罚没押金
            for (RptIncomeInfoEntity fv:fs){
                if(fv.getSourceType().equals(v.getSourceType())&&fv.getProjectId().equals(v.getProjectId())){
                    v.setFineNumber(fv.getFineNumber());
                }
            }
            v.setSummaryNumber(v.getFineNumber());
            list.add(v);
            RptIncomeInfoEntity rv = getSourceTypeIncomeInfo(v.getProjectId(),v.getSourceType(),rs);
            RptIncomeInfoEntity lv = getSourceTypeIncomeInfo(v.getProjectId(),v.getSourceType(),ls);
            RptIncomeInfoEntity lv1 = getSourceTypeIncomeInfo(v.getProjectId(),v.getSourceType(),ls1);
            RptIncomeInfoEntity lv2 = getSourceTypeIncomeInfo(v.getProjectId(),v.getSourceType(),ls2);
            RptIncomeInfoEntity sv = getSourceTypeIncomeInfo(v.getProjectId(),v.getSourceType(),ss);
            if(rv!=null){
                list.add(rv);
            }
            if(lv!=null){
                list.add(lv);
            }
            if(lv1!=null){
                list.add(lv1);
            }
            if(lv2!=null){
                list.add(lv2);
            }
            if(sv!=null){
                list.add(sv);
            }
//            list.add(av);
        }
        //删除今天已生成的报表数据
        incomeInfoDao.delByDay(DateUtil.today());
        for (RptIncomeInfoEntity info: list) {
            info.setReportDate(DateUtil.date());
            info.insert();
        }
        return list;
    }


    private RptIncomeInfoEntity getSourceTypeIncomeInfo(String projectId,String sourceType,List<RptIncomeInfoEntity> vos){
        for (RptIncomeInfoEntity fv:vos){
            if(fv.getSourceType().equals(sourceType)&&fv.getProjectId().equals(projectId)){
                if(null!=fv.getCollectedNumber()&&fv.getCollectedNumber().compareTo(BigDecimal.ZERO)>0){
                    BigDecimal summary = BigDecimal.ZERO;
                    summary = fv.getCollectedNumber();
                    if(null!=fv.getRefundNumber()){
                        summary = fv.getCollectedNumber().subtract(fv.getRefundNumber());
                        if(summary.compareTo(BigDecimal.ZERO)<0){
                            summary = BigDecimal.ZERO;
                        }
                    }
                    fv.setSummaryNumber(summary);
                }
                return fv;
            }
        }
        return null;
    }
}
