package cn.uone.business.cont.service;

import cn.uone.bean.entity.business.base.BaseEnterpriseEntity;
import cn.uone.bean.entity.business.biz.vo.SettleVo;
import cn.uone.bean.entity.business.cont.ContFrameContractEntity;
import cn.uone.bean.entity.business.cont.vo.ContFrameContractSearchVo;
import cn.uone.bean.entity.business.cont.vo.ContFrameContractVo;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.mybatis.inerceptor.DataScope;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-28
 */
public interface IContFrameContractService extends IService<ContFrameContractEntity> {

    IPage<ContFrameContractVo> selectPages(Page page, ContFrameContractSearchVo param);

    IPage<HashMap> findSourceByCondition(Page page, DataScope dataScope, String id,String sourceName, String partitionId, String floor, String relType, String sourceType, List<String> sourceIds);

    Boolean existFrameContractByTempId(String tempId);

    boolean createContract(ContFrameContractEntity frameContract, BaseEnterpriseEntity enterprise, List<MultipartFile> licenseFiles, List<MultipartFile> contractFiles, List<MultipartFile> annexFiles, String contractVos);

    void validContract(String id) throws Exception;

    @Transactional
    void cancelContract(String id) throws Exception;

    RestResponse endContract(SettleVo vo);

    boolean createMultiSourceContract(ContFrameContractEntity frameContract, RenterEntity renter, List<MultipartFile> licenseFiles, List<MultipartFile> contractFiles, List<MultipartFile> annexFiles, String contractVos) throws Exception;


}
