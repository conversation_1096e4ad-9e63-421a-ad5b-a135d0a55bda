package cn.uone.business.common.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.uone.bean.entity.business.kingdee.KingdeePaymentEntity;
import cn.uone.bean.entity.business.kingdee.vo.KingdeeReceiptSearchVo;
import cn.uone.bean.entity.business.kingdee.vo.KingdeeReceiptVo;
import cn.uone.business.bpm.service.IBpmWorkflowService;
import cn.uone.business.cont.service.IContContractService;
import cn.uone.business.cont.service.IContTempService;
import cn.uone.business.kingdee.service.IKingdeeApiService;
import cn.uone.business.kingdee.service.IKingdeePaymentService;
import cn.uone.business.kingdee.service.IKingdeeReceiptService;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.UoneLog;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @ClassName XyBPMController
 * @Description BPM-接口回调
 * <AUTHOR>
 * @Date 2021/5/13 15:38
 * @Version 1.0
 */
@RestController
@RequestMapping("/xyKingdee")
public class XyKingdeeController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(XyKingdeeController.class);

    @Autowired
    private IContContractService contractService;
    @Autowired
    private IContTempService tempService;
    @Autowired
    private IBpmWorkflowService bpmWorkflowService;
    @Autowired
    IKingdeeReceiptService kingdeeReceiptService;
    @Autowired
    IKingdeeApiService kingdeeApiService;
    @Autowired
    IKingdeePaymentService kingdeePaymentService;

    @RequestMapping("/getReceiptList")
    @UoneLog("获取收款单列表")
    public RestResponse getReceiptList(@RequestBody KingdeeReceiptSearchVo searchVo) throws Exception {
        if(searchVo.getStartEntryDate()==null && searchVo.getEndEntryDate()==null && StrUtil.isBlank(searchVo.getCompany())){
            return RestResponse.failure("参数不能为空");
        }
        Map<String,Object> map = Maps.newHashMap();
        map.put("searchVo",searchVo);
        List<KingdeeReceiptVo> list = kingdeeReceiptService.selectVoByMap(map);
        return RestResponse.success().setData(list);
    }

    @RequestMapping("/getShowUrl")
    @UoneLog("获取查看单据地址")
    public RestResponse getShowUrl(@RequestParam("kingdeeId") String kingdeeId,@RequestParam("bosType") String bosType) {

//        Map<String,Object> map = Maps.newHashMap();
//        map.put("searchVo",searchVo);
//        List<KingdeeReceiptVo> list = kingdeeReceiptService.selectVoByMap(map);
        return RestResponse.success();
    }

    @RequestMapping("/kingdeePush")
    @UonePermissions
    @UoneLog("推送")
    public RestResponse kingdeePush() throws Exception {
        kingdeeApiService.createPaymentAndTransferBill("d96082a179092bb02a578533f8bfd130","3d5d3b30b9c35469253b4a24c304d751","00302954d9b913a408d67d4aee9b4tcy");
        return RestResponse.success();
    }

    @RequestMapping("/paymentNotify")
    @UonePermissions
    @UoneLog("付款单支付回调")
    public RestResponse paymentNotify(@RequestBody Map<String,Object> resMap) {
        try {
            if(MapUtil.isEmpty(resMap)){
                return RestResponse.failure("请回传参数");
            }
            if(!(resMap.containsKey("paymentId") && resMap.containsKey("paymentStatus") && resMap.containsKey("paymentDate"))){
                return RestResponse.failure("请回传正确的参数");
            }
            //付款单id
            String paymentId = (String) resMap.get("paymentId");
            //付款单状态
            String paymentStatus = (String) resMap.get("paymentStatus");
            //付款日期
            String paymentDate = (String) resMap.get("paymentDate");

            System.out.println("#########paymentNotify###########:"+ JSONUtil.toJsonStr(resMap));

            //业务逻辑处理请完善。。。
            KingdeePaymentEntity paymentEntity = kingdeePaymentService.getById(paymentId);
            if(paymentEntity == null){
                return RestResponse.failure("长租系统不存在匹配的付款单");
            }
            paymentEntity.setPaymentStatus(paymentStatus);
            paymentEntity.setPaymentDate(DateUtil.parseDate(paymentDate));
            kingdeePaymentService.updateById(paymentEntity);
        } catch (Exception e) {
            e.printStackTrace();
            return RestResponse.failure(e.getMessage());
        }
        //业务逻辑end
        return RestResponse.success();
    }

    public static void main(String[] args) {
        Map<String,Object> paras = Maps.newHashMap();
        paras.put("startEntryDate","2021-06-22");
        paras.put("endEntryDate","2021-06-23");
        paras.put("company","SHMYFD");
        String json = HttpUtil.get("http://localhost:8032/xyKingdee/getReceiptList", paras);
        System.out.println(json);
    }

}
