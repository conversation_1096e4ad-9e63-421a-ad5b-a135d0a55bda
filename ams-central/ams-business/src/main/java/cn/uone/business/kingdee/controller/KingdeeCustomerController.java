package cn.uone.business.kingdee.controller;


import cn.uone.business.kingdee.service.IKingdeeCustomerService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-09
 */
@RestController
@RequestMapping("/kingdee-customer-entity")
public class KingdeeCustomerController extends BaseController {
    @Autowired
    IKingdeeCustomerService kingdeeCustomerService;

    @RequestMapping("/getList")
    public RestResponse getList() {
        String projectId = UoneHeaderUtil.getProjectId();
        QueryWrapper query = new QueryWrapper();
        query.eq("project_id",projectId);
        query.select("distinct code,name");
        RestResponse response = new RestResponse();
        return response.setSuccess(true).setData(kingdeeCustomerService.list(query));
    }
}
