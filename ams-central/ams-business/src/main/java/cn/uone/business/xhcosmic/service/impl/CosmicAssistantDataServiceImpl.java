package cn.uone.business.xhcosmic.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.application.enumerate.cosmic.AssistantDataTypeEnum;
import cn.uone.bean.entity.business.xhcosmic.CosmicAssistantDataEntity;
import cn.uone.bean.entity.tpi.cosmic.BaseQueryPageVo;
import cn.uone.business.xhcosmic.dao.CosmicAssistantDataDao;
import cn.uone.business.xhcosmic.service.ICosmicAssistantDataService;
import cn.uone.fegin.tpi.cosmic.ICosmicBaseFeign;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 金蝶财务(星瀚)辅助资料表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Service
public class CosmicAssistantDataServiceImpl extends ServiceImpl<CosmicAssistantDataDao, CosmicAssistantDataEntity> implements ICosmicAssistantDataService {

    @Autowired
    private ICosmicBaseFeign cosmicBaseFeign;

    @Override
    @Transactional
    public List<CosmicAssistantDataEntity> insertBatch(List<CosmicAssistantDataEntity> entities) {
        if (CollectionUtil.isEmpty(entities)) {
            return entities;
        }
        //查询到重复的数据集合使用更新
        List<CosmicAssistantDataEntity> duplicateList = baseMapper.duplicateList(entities);
        Map<String, CosmicAssistantDataEntity> duplicateTypeAndNumberMap = duplicateList.stream().collect(Collectors.toMap(entity -> entity.getTypeNumber() + StringPool.COLON + entity.getNumber(), Function.identity(), (a, b) -> b));
        entities.forEach(entity -> {
            entity.setId(duplicateTypeAndNumberMap.getOrDefault(entity.getTypeNumber() + StringPool.COLON + entity.getNumber(), new CosmicAssistantDataEntity()).getId());
        });
        saveOrUpdateBatch(entities);
        return entities;
    }

    @Override
    public List<CosmicAssistantDataEntity> query(AssistantDataTypeEnum assistantDataTypeEnum, String number) {
        return list(
                new LambdaQueryWrapper<CosmicAssistantDataEntity>()
                        .eq(CosmicAssistantDataEntity::getTypeNumber, assistantDataTypeEnum.getTypeNumber())
                        .eq(StrUtil.isNotBlank(number), CosmicAssistantDataEntity::getNumber, number)
        );
    }

    @Override
    @Transactional
    public List<CosmicAssistantDataEntity> queryFormCosmicApi(AssistantDataTypeEnum assistantDataTypeEnum, String number) {
        RestResponse response = cosmicBaseFeign.assistantDataQuery(assistantDataTypeEnum.getTypeNumber(), 1, 1000);
        if (BooleanUtil.isTrue(response.getSuccess())) {
            BaseQueryPageVo<JSONObject> baseQueryPageVo = JSONUtil.toBean(new JSONObject(MapUtil.getAny(response, "data")), new TypeReference<BaseQueryPageVo<JSONObject>>() {
            }, true);
            List<CosmicAssistantDataEntity> entityList = baseQueryPageVo.getRows()
                    .stream().map(jsonObject -> mapToAssistantData(assistantDataTypeEnum, jsonObject)).collect(Collectors.toList());
            List<CosmicAssistantDataEntity> cosmicAssistantDataEntities = insertBatch(entityList);
            if (StrUtil.isNotBlank(number)) {
                return cosmicAssistantDataEntities.stream().filter(entity ->
                        assistantDataTypeEnum.getTypeNumber().equals(entity.getTypeNumber()) && entity.getNumber().equals(number)
                ).collect(Collectors.toList());
            }
            return cosmicAssistantDataEntities;
        }
        return Collections.emptyList();
    }

    @Override
    @Transactional
    public CosmicAssistantDataEntity queryOne(AssistantDataTypeEnum assistantDataTypeEnum, String number) {
        List<CosmicAssistantDataEntity> assistantDataEntities = query(assistantDataTypeEnum, number);
        if (CollectionUtil.isNotEmpty(assistantDataEntities)) {
            return assistantDataEntities.get(0);
        }
        List<CosmicAssistantDataEntity> cosmicAssistantDataEntities = queryFormCosmicApi(assistantDataTypeEnum, number);
        return CollectionUtil.isNotEmpty(cosmicAssistantDataEntities) ? cosmicAssistantDataEntities.get(0) : new CosmicAssistantDataEntity();
    }


    private CosmicAssistantDataEntity mapToAssistantData(AssistantDataTypeEnum assistantDataTypeEnum, JSONObject jsonObject) {
        return new CosmicAssistantDataEntity()
                .setTypeNumber(assistantDataTypeEnum.getTypeNumber())
                .setTypeName(assistantDataTypeEnum.getTypeName())
                .setNumber(jsonObject.getStr("number"))
                .setName(jsonObject.getStr("name"));
    }
}
