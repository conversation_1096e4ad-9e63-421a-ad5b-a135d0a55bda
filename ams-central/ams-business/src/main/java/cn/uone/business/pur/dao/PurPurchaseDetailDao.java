package cn.uone.business.pur.dao;

import cn.uone.bean.entity.business.pur.PurPurchaseDetailEntity;
import cn.uone.bean.entity.business.pur.vo.PurPurchaseDetailEntityVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Map;


/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface PurPurchaseDetailDao extends BaseMapper<PurPurchaseDetailEntity> {
    Integer queryDeviceByPurchaseId(String purchaseId);

    PurPurchaseDetailEntityVo queryPurchaseDetailLinkDevDevice(@Param("map")Map map);

    IPage<PurPurchaseDetailEntityVo> queryDetailDev(Page page, @Param("purchaseId")String purchaseId);
}
