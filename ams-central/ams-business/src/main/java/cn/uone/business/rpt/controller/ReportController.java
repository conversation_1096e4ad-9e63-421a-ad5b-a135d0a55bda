package cn.uone.business.rpt.controller;

import cn.uone.bean.entity.business.report.vo.ReportReceiptVo;
import cn.uone.bean.parameter.ReportReceiptPo;
import cn.uone.business.rpt.dao.ReportReceiptDao;
import cn.uone.util.ExcelRender;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 报表
 */
@RestController
@RequestMapping("/report")
public class ReportController {

    @Resource
    private ReportReceiptDao receiptDao;

    @RequestMapping(value = "/receipt/page", method = RequestMethod.GET)
    public RestResponse page(Page page, ReportReceiptPo param) {
        return RestResponse.success().setData(receiptDao.selectPage(page,param));
    }

    @RequestMapping(value = {"/receipt/export"}, method = RequestMethod.POST)
    public void rentAccountExport(HttpServletResponse response, ReportReceiptPo param) throws BusinessException {

        if(null!=param.getIds()&&param.getIds().size()>0){
            List<String> ids = param.getIds();
            param = new ReportReceiptPo();
            param.setIds(ids);
        }
        List<ReportReceiptVo> list =  receiptDao.selectPage(param);
        Map<String, Object> beans = Maps.newHashMap();
        beans.put("vo", list);
        ExcelRender.me("/excel/export/receipt.xlsx").beans(beans).render(response);
    }
}
