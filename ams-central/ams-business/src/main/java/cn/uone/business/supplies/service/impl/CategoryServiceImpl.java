package cn.uone.business.supplies.service.impl;


import cn.uone.bean.entity.business.supplies.CategoryEntity;
import cn.uone.bean.entity.business.supplies.vo.CategoryVo;
import cn.uone.business.supplies.dao.CategoryDao;
import cn.uone.business.supplies.service.ICategoryService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 物料类别表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-29
 */
@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryDao, CategoryEntity> implements ICategoryService {


    @Override
    public IPage<CategoryVo> getApplyByUser(Page page, String userId) {
        return baseMapper.getApplyByUser(page,userId);
    }
}
