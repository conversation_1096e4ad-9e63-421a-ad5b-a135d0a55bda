package cn.uone.business.investment.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.investment.InvestmentResourceEntity;
import cn.uone.business.investment.service.IInvestmentResourceService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 品牌库资源 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-27
 */
@RestController
@RequestMapping("/investment/resource")
public class InvestmentResourceController extends BaseController {

    @Autowired
    private IInvestmentResourceService service;


    @GetMapping("/page")
    public RestResponse page(Page<InvestmentResourceEntity> page, InvestmentResourceEntity entity){
        QueryWrapper<InvestmentResourceEntity> wrapper = new QueryWrapper<>();
        if(StrUtil.isNotBlank(entity.getBusinessName())){
            wrapper.like("business_name ","%"+entity.getBusinessName()+"%");
        }
        if(StrUtil.isNotBlank(entity.getCategoryName())){
            wrapper.like("category_name ","%"+entity.getCategoryName()+"%");
        }
        if(StrUtil.isNotBlank(entity.getName())){
            wrapper.like("name ","%"+entity.getName()+"%");
        }
        wrapper.orderByDesc("create_date");
        IPage<InvestmentResourceEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

    @PostMapping("/addOrUpdate")
    public RestResponse addOrUpdate(@ModelAttribute InvestmentResourceEntity entity, @RequestParam(required = false) List<MultipartFile> resourceFiles,
                                    @RequestParam(required = false) List<MultipartFile> imageFile){
        service.saveOrUpdate(entity,resourceFiles,imageFile);
        return RestResponse.success();
    }

//    @PostMapping("/save")
//    public RestResponse save(InvestmentResourceEntity entity){
//        entity.setStatus("1");
//        entity.insertOrUpdate();
//        return RestResponse.success();
//    }


    @PostMapping("/edit")
    public RestResponse edit(InvestmentResourceEntity entity){
        entity.updateById();
        return RestResponse.success();
    }

    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        service.removeByIds(ids);
        return RestResponse.success();
    }
}
