package cn.uone.business.res.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.res.ResProjectHotEntity;
import cn.uone.bean.entity.business.res.vo.ResProjectHotVo;
import cn.uone.business.res.service.IResProjectHotService;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-16
 */
@RestController
@RequestMapping("/projecthot")
public class ResProjectHotController extends BaseController {

    @Autowired
    private IResProjectHotService resProjectHotService;

    @RequestMapping("/queryPage")
    public RestResponse queryPage(String name, Page page) {
        IPage<ResProjectHotVo> list = resProjectHotService.queryPage(name, page);
        return RestResponse.success().setData(page);
    }


    @RequestMapping("/queryList")
    @UonePermissions
    public RestResponse queryList() {
        List<ResProjectHotVo> list = resProjectHotService.queryList();
        return RestResponse.success().setData(list);
    }

    //TODO modify by zengguoshen 20211014  start  租客商热门门店展示
    @RequestMapping("/showList")
    @UonePermissions
    public RestResponse showList() {
        List<ResProjectHotVo> list = resProjectHotService.showList();
        return RestResponse.success().setData(list);
    }
    //TODO modify by zengguoshen 20211014  end

    @RequestMapping("/save")
    public RestResponse save(ResProjectHotEntity entity) {
        if (StrUtil.isNotEmpty(entity.getId())) {
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.ne("id", entity.getId());
            wrapper.eq("project_id", entity.getProjectId());
            List<ResProjectHotEntity> list = resProjectHotService.list(wrapper);
            if (list.size() > 0) {
                return RestResponse.failure("该项目已在列表页，可直接编辑");
            }
        }
        resProjectHotService.saveOrUpdate(entity);
        return RestResponse.success().setData(entity);
    }

    @RequestMapping("/del")
    public RestResponse del(@RequestParam(value = "id", required = true) String id) {
        resProjectHotService.removeById(id);
        return RestResponse.success();
    }

    @RequestMapping(value = "/cancelState", method = RequestMethod.POST)
    public RestResponse cancelState(@RequestParam(value = "id", required = true) String id) {
        ResProjectHotEntity entity = resProjectHotService.getById(id);
        if (entity == null) {
            return RestResponse.failure("该项目不存在");
        }
        entity.setState("0");
        resProjectHotService.saveOrUpdate(entity);
        return RestResponse.success();
    }

    @RequestMapping("/toState")
    public RestResponse toState(@RequestParam(value = "id", required = true) String id) {
        ResProjectHotEntity entity = resProjectHotService.getById(id);
        if (entity == null) {
            return RestResponse.failure("该项目不存在");
        }
        entity.setState("1");
        resProjectHotService.saveOrUpdate(entity);
        return RestResponse.success();
    }

    @RequestMapping("/queryUpdate")
    public RestResponse queryUpdate(@RequestParam(value = "id", required = true) String id) {
        ResProjectHotEntity entity = resProjectHotService.getById(id);
        if (entity == null) {
            return RestResponse.failure("该热门项目不存在");
        }
        return RestResponse.success().setData(entity);
    }

}
