package cn.uone.business.face.dao;

import cn.uone.bean.entity.business.face.FaceAuditEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 人脸注册表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
public interface FaceAuditDao extends BaseMapper<FaceAuditEntity> {
    List<Map<String,Object>> selectFaceAuditList(@Param("map") Map<String,Object> map);
}
