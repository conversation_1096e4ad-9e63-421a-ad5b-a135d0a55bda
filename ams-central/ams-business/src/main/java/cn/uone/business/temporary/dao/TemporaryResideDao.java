package cn.uone.business.temporary.dao;

import cn.uone.bean.entity.business.temporary.TemporaryResideEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 临时用房表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
public interface TemporaryResideDao extends BaseMapper<TemporaryResideEntity> {

    IPage<TemporaryResideEntity> findByCondition(Page page, @Param("map") Map<String, Object> map);

    TemporaryResideEntity getLastOneBySourceId(String sourceId);

}
