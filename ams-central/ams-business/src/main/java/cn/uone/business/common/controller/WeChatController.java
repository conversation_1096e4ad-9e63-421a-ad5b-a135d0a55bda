package cn.uone.business.common.controller;

import cn.uone.bean.entity.business.bil.AutoReplyEntity;
import cn.uone.business.bil.service.IAutoReplyService;
import cn.uone.business.util.WeChatMessageUtil;
import cn.uone.fegin.tpi.IWechatFegin;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.security.MessageDigest;
import java.util.*;

@RestController
@RequestMapping("/common/weChat")
public class WeChatController extends BaseController {

    private String Token = "Lin20088";

    private String encodingAESKey = "zwsKTrJJBOGp0BK4rIqyAtjjerFabxGi8GULDMFtMc3";

    private static final char[] HEX_DIGITS = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd',
            'e', 'f' };

    @Autowired
    private IAutoReplyService autoReplyService;

    @Autowired
    private IWechatFegin wechatFegin;

    @RequestMapping(value = "/getAppId" , method = {RequestMethod.GET,RequestMethod.POST})
    @UonePermissions
    public RestResponse getAppId(){
        return RestResponse.success().setAny("appid",wechatFegin.getWechatAppId());
    }

    @RequestMapping(value = "/getSpecMovie" , method = {RequestMethod.GET,RequestMethod.POST})
    @UonePermissions
    public void getSpecMovie(HttpServletRequest request , HttpServletResponse response){
        response.setContentType("text/html;charset=utf-8");
        System.out.println("进入方法");
        Boolean isGet = request.getMethod().toLowerCase().equals("get");
        if (isGet){
            String signature = request.getParameter("signature");
            String timestamp = request.getParameter("timestamp");
            String nonce = request.getParameter("nonce");
            String echostr = request.getParameter("echostr");
            System.out.println(signature);
            System.out.println(timestamp);
            System.out.println(nonce);
            System.out.println(echostr);
            access(request, response);
        }else{
            // 进入POST聊天处理
            System.out.println("enter post");
            try {
                // 接收消息并返回消息
                acceptMessage(request, response);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 验证url真实性
     * @param request
     * @param response
     * @return
     */
    private String access(HttpServletRequest request , HttpServletResponse response){

        // 验证URL真实性
        System.out.println("进入验证access");
        String signature = request.getParameter("signature");// 微信加密签名
        String timestamp = request.getParameter("timestamp");// 时间戳
        String nonce = request.getParameter("nonce");// 随机数
        String echostr = request.getParameter("echostr");// 随机字符串
        List<String> params = new ArrayList<String>();
        params.add(Token);
        params.add(timestamp);
        params.add(nonce);
        // 1. 将token、timestamp、nonce三个参数进行字典序排序
        Collections.sort(params, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.compareTo(o2);
            }
        });
        // 2. 将三个参数字符串拼接成一个字符串进行sha1加密
        String temp = encode(params.get(0) + params.get(1) + params.get(2));
        if (temp.equals(signature)) {
            try {
                response.getWriter().write(echostr);
                System.out.println("成功返回 echostr：" + echostr);
                return echostr;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        System.out.println("失败 认证");
        return null;

    }
    private void acceptMessage(HttpServletRequest request, HttpServletResponse response) throws IOException {
        PrintWriter out = null;
        //将微信请求xml转为m，获取所需的参数
        Map<String,String> map = WeChatMessageUtil.xmlToMap(request);
        String ToUserName = map.get("ToUserName");
        String FromUserName = map.get("FromUserName");
        String MsgType = map.get("MsgType");
        String Content = map.get("Content");
        String message = null;
        //处理文本类型，实现输入1，回复相应的封装的内容
        if("text".equals(MsgType)){
            // TODO 根据传入的值去数据库匹配对应的返回回答
            AutoReplyEntity autoReplyEntity = autoReplyService.getEntityByKeyWord(Content);
            String content = "很高兴为您服务,您有任何问题都可以拨打*****咨询!";
            if(null!=autoReplyEntity){
                content = autoReplyEntity.getReply();
            }else{

            }
            WeChatMessageUtil textMessage = new WeChatMessageUtil();
            message = textMessage.initMessage(FromUserName, ToUserName,content);
        }
        try {
            out = response.getWriter();
            out.write(message);
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        out.close();
    }

    private static String getFormattedText(byte[] bytes) {
        int len = bytes.length;
        StringBuilder buf = new StringBuilder(len * 2);
        // 把密文转换成十六进制的字符串形式
        for (int j = 0; j < len; j++) {
            buf.append(HEX_DIGITS[(bytes[j] >> 4) & 0x0f]);
            buf.append(HEX_DIGITS[bytes[j] & 0x0f]);
        }
        return buf.toString();
    }
    /**
     * 微信接入需要的加密
     * @param str
     * @return
     */
    public static String encode(String str) {
        if (str == null) {
            return null;
        }
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA1");
            messageDigest.update(str.getBytes());
            return getFormattedText(messageDigest.digest());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
