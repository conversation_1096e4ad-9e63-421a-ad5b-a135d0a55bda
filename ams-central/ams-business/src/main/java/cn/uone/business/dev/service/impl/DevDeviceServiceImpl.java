package cn.uone.business.dev.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.ThirdSupplierTypeEnum;
import cn.uone.application.enumerate.ZDYSupplierTypeEnum;
import cn.uone.bean.entity.business.cont.ContContractEntity;
import cn.uone.bean.entity.business.dev.DevDeviceEntity;
import cn.uone.bean.entity.business.dev.DevDeviceLogEntity;
import cn.uone.bean.entity.business.dev.DevLockPasswordEntity;
import cn.uone.bean.entity.business.dev.vo.DevDeviceEntitySerchVo;
import cn.uone.bean.entity.business.dev.vo.DevDeviceEntityVo;
import cn.uone.bean.entity.business.res.vo.ResSourceDeviceInfo;
import cn.uone.bean.entity.business.res.vo.ResSourceVo;
import cn.uone.bean.entity.crm.RenterEntity;
import cn.uone.business.dev.dao.DevDeviceDao;
import cn.uone.business.dev.service.IDevDeviceLogService;
import cn.uone.business.dev.service.IDevDeviceService;
import cn.uone.business.dev.service.IDevLockPasswordService;
import cn.uone.business.dev.service.IDevSupplierService;
import cn.uone.business.res.service.IResSourceService;
import cn.uone.business.sys.service.ISysPushMsgService;
import cn.uone.fegin.crm.IRenterFegin;
import cn.uone.fegin.crm.ISysMsgTemplateFegin;
import cn.uone.fegin.tpi.*;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Service
@Slf4j
public class DevDeviceServiceImpl extends ServiceImpl<DevDeviceDao, DevDeviceEntity> implements IDevDeviceService {

    @Autowired
    private IDevLockPasswordService devLockPasswordService;
    @Autowired
    private IResSourceService resSourceService;
    @Autowired
    private IMeterFegin meterFegin;
    @Autowired
    private IGuojiaMeterFegin guojiaMeterFegin;
    @Autowired
    private IKeluFegin keluFegin;
    @Autowired
    private IYundingFegin yundingFegin;
    @Autowired
    private IDanbayFegin danbayFegin;
    @Resource
    private DevDeviceDao deviceDao;
    @Autowired
    private IDevSupplierService devSupplierService;
    @Autowired
    private IRenterFegin renterFegin;
    @Autowired
    private ISysMsgTemplateFegin sysMsgTemplateFegin;
    @Autowired
    private ISysPushMsgService sysPushMsgService;
    @Autowired
    private IGzxyDoorLockFegin gzxyDoorLockFegin;
    @Autowired
    private ITqDianbiaoFegin tqDianbiaoFegin;
    @Autowired
    private IYkWaterFegin ykWaterFegin;
    @Autowired
    @Lazy
    private IDevDeviceLogService devDeviceLogService;



    @Override
    public void addDevDevice(DevDeviceEntity devDeviceEntity) {
        baseMapper.insert(devDeviceEntity);
    }

    @Override
    public List<DevDeviceEntity> queryDevDevice(DevDeviceEntity devDeviceEntity) {
        QueryWrapper<DevDeviceEntity> wrapper=new QueryWrapper<>();
        if(StringUtils.isNotBlank(devDeviceEntity.getCode())){
            wrapper.eq("code",devDeviceEntity.getCode());
        }
        if(StringUtils.isNotBlank(devDeviceEntity.getClassId())){
            wrapper.eq("class_id",devDeviceEntity.getClassId());
        }
        List<DevDeviceEntity> list=baseMapper.selectList(wrapper);
        return list;
    }

    @Override
    public void updateDevDevice(DevDeviceEntity devDeviceEntity) {
        QueryWrapper queryWrapper =new QueryWrapper();
        queryWrapper.eq("purchase_detail_id",devDeviceEntity.getPurchaseDetailId());
        baseMapper.update(devDeviceEntity,queryWrapper);
    }

    @Override
    public DevDeviceEntity getByCode(String code) {
        QueryWrapper<DevDeviceEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("code", code);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<DevDeviceEntityVo> queryDevListBySourceId(String sourceId) {
        return baseMapper.queryDevListBySourceId(sourceId);
    }

    @Override
    public List<DevDeviceEntityVo> queryDevByNameAndSourid(Map map){
        return baseMapper.queryDevByNameAndSourid(map);
    }

    @Override
    public List<DevDeviceEntityVo> getAiDevices(Map map) {
        return baseMapper.getAiDevices(map);
    }

    @Override
    public Map<String, Boolean> havingDevice(String sourceId) {
        Map<String, Boolean> result = new HashMap<>();

        //用于标记有 公共的 智能水表设备
        boolean waterAiDevPb = false;

        //用于标记有 非公共的 智能水表设备
        boolean waterAiDev = false;

        //用于标记有 公共的 智能电表设备
        boolean elecAiDevPb = false;

        //用于标记有 非公共的 智能电表设备
        boolean elecAiDev = false;

        Map<String, Object> par = new HashMap<>();
        par.put("sourceId",sourceId);
        List<DevDeviceEntityVo> aiDevices = this.getAiDevices(par);
        if(!CollectionUtils.isEmpty(aiDevices)){
            for (DevDeviceEntityVo aiDevice : aiDevices) {
                if(BaseConstants.BOOLEAN_OF_TRUE.equals(aiDevice.getIsPublic())){
                    //公共设备
                    if("智能水表".equals(aiDevice.getTypeName())){
                        waterAiDevPb = true;
                    }else if("智能电表".equals(aiDevice.getTypeName())){
                        elecAiDevPb = true;
                    }
                }else{
                    //非公共设备
                    if("智能水表".equals(aiDevice.getTypeName())){
                        waterAiDev = true;
                    }else if("智能电表".equals(aiDevice.getTypeName())){
                        elecAiDev = true;
                    }
                }
            }
        }
        result.put("waterAiDevPb",waterAiDevPb);
        result.put("elecAiDevPb",elecAiDevPb);
        result.put("waterAiDev",waterAiDev);
        result.put("elecAiDev",elecAiDev);
        return result;
    }

    /**
     * 冻结门锁密码
     */
    @Override
    public RestResponse meterFrozen(String sourceId, String renterId) throws Exception {
        if (StrUtil.isBlank(sourceId)) {
            return RestResponse.failure("门锁冻结失败,sourceId参数为空");
        }
        if (StrUtil.isBlank(renterId)) {
            return RestResponse.failure("门锁冻结失败,renterId参数为空");
        }
        //先查出该房源下的所有门锁
        Map<String, Object> menjinMap = Maps.newHashMap();
        menjinMap.put("sourceId", sourceId);
        menjinMap.put("name", "智能门锁");
        menjinMap.put("type", "0");
        List<DevDeviceEntityVo> deviceList = this.queryDevByNameAndSourid(menjinMap);
        if (deviceList.size() > 0) {
            for (DevDeviceEntityVo deviceEntityVo : deviceList) {
                String supplierId = deviceEntityVo.getSupplierId();
                //由原来的根据id获得supplierType，改成用name获得type。
                String name = devSupplierService.getNameById(supplierId);
                String supplierType = ZDYSupplierTypeEnum.getValueById(name);
                //查出租客在该门锁的授权记录(锁有效时间为空的数据在前面)
                DevLockPasswordEntity lockPasswordEntity = devLockPasswordService.queryNullFirst(deviceEntityVo.getId(), renterId);
                //有授权记录的话就对该密码进行冻结
                if (ObjectUtil.isNotNull(lockPasswordEntity)) {
                    ResSourceVo sourceVo = resSourceService.getInfoById(sourceId);
                    String sourceName = sourceVo.getHouseName();
                    log.info("自动调用门锁冻结参数，deviceCode:" + deviceEntityVo.getCode() + ";supplierType；" + supplierType + ";pwdId:" + lockPasswordEntity.getPasswordId());
                    RenterEntity renter = renterFegin.getById(renterId);
                    RestResponse response = frozenResponse(deviceEntityVo.getCode(), deviceEntityVo.getSupplierId(), lockPasswordEntity.getPasswordId(), deviceEntityVo.getProjectId(),renter.getTel());
                    if ((boolean) response.get("success") || "密码已经过期".equals(response.getMessage()) || "密码有效期已过，不支持该操作".equals(response.getMessage())) {
                        lockPasswordEntity.deleteById();
                    } else if(!"房间不存在该房客".equals(response.getMessage())){
                        return RestResponse.failure("门锁冻结失败,原因：" + response.getMessage());
                    }
                }
            }
        }
        return RestResponse.success();
    }

    @Override
    public Boolean JudgeGuojia(String supplierType, String supplierId) throws BusinessException {
        //改成用供应商名为条件，来获得value
        String name=devSupplierService.getNameById(supplierId);
        if (StrUtil.isBlank(supplierType)) {
            supplierType = ThirdSupplierTypeEnum.getValueById(name);
            if (StrUtil.isBlank(supplierType)) {
                throw new BusinessException("该设备:" + supplierId+" "+name + " ,对应的供应商无法操作");
            }
            return true;
        } else {
            return false;
        }
    }

    @Override
    public String JudgeMeterType(String supplierType, String supplierId) throws BusinessException {
       //改成用供应商名为条件，来获得value
        String name=devSupplierService.getNameById(supplierId);
        if (StrUtil.isBlank(supplierType)) {//supplierType不在ZDYSupplierTypeEnum中时,从ThirdSupplierTypeEnum中获得供应商
            supplierType = ThirdSupplierTypeEnum.getValueById(name);
            if (StrUtil.isBlank(supplierType)) {
                throw new BusinessException("该设备:" + supplierId +" "+name +" ,对应的供应商无法操作");
            }
            if (supplierType.equals(ThirdSupplierTypeEnum.Kelu.getValue())) {
                return "2";
            } else if(supplierType.equals(ThirdSupplierTypeEnum.Danbay.getValue())){
                return "3";
            }
            else {
                return BaseConstants.BOOLEAN_OF_TRUE;
            }
        } else {
            if (supplierType.equals(ZDYSupplierTypeEnum.YUNDING.getValue())) {
                return "4";
            }
            return BaseConstants.BOOLEAN_OF_FALSE;
        }
    }

    @Override
    public void authorization(ContContractEntity contract, String sourceId, String renterId) throws Exception {
        //生成密码
        String password = RandomUtil.randomNumbers(6);
        //获得门禁设备
        Map<String, Object> menjinMap = Maps.newHashMap();
        menjinMap.put("sourceId", sourceId);
        menjinMap.put("name", "智能门锁");
        menjinMap.put("type", "0");
        List<DevDeviceEntityVo> deviceList = this.queryDevByNameAndSourid(menjinMap);
        if(deviceList.size()>0) {
            DevDeviceEntity device = deviceList.get(0);
            //获得入住人信息（此处传入的必须为入住人的id，不能是合同签约人的id）
            RenterEntity renter = renterFegin.getById(renterId);
            //获得合同信息
            SimpleDateFormat sformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//日期格式
            String endDate = sformat.format(contract.getEndDate());
            String startDate = sformat.format(contract.getStartDate());
            //String startDate = sformat.format(new Date());
            //进行授权操作
            RestResponse response = authorizationResponse(device.getCode(), device.getSupplierId(), password, renter.getTel(), startDate, endDate, device.getProjectId(),renter.getName());
            //授权后短信发送密码,并保存密码的信息
            if ((boolean) response.get("success")) {
                String pswid = savePsw(device.getId(), response, renterId, password, contract.getEndDate());
                ResSourceVo sourceVo = resSourceService.getInfoById(sourceId);
                //TODO modify by zengguoshen 20xxxxxx  start 发送密码
                //发送短信
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("password", password);
                params.put("house",sourceVo.getHouseName());
//                params.put("template_code", "197788");
                // modify by linderen on 20210714 修改通知方式为公众号通知 start
                sysMsgTemplateFegin.sendByProjectId(sourceVo.getProjectId(), "19999",renter.getTel(), JSONUtil.toJsonStr(params));
                params.put("title","房屋密码下发");
                params.put("renterId",renter.getId());
                params.put("template_code","SMS_471435150");
                sysPushMsgService.pushRenterMsg(params);
            }
        }

    }


    //保存密码
    @Override
    public String savePsw(String deviceId, RestResponse response, String renterId, String password, Date endDate) {
        DevLockPasswordEntity devlock=new DevLockPasswordEntity();
        devlock.setDeviceId(deviceId);
        if(ObjectUtil.isNotNull(response.get("passwordId"))){
            devlock.setPasswordId((String) response.get("passwordId"));
        }
        devlock.setRenterId(renterId);
        devlock.setPassword(password);
        devlock.setEndTime(endDate);
        devLockPasswordService.save(devlock);
        return devlock.getId();
    }

    @Override
    public RestResponse authorizationResponse(String code, String supplierId, String password, String tel, String startDate, String endDate, String projectId,String rentName) throws Exception {
        //由原来的根据id获得supplierType，改成用name获得type。
        String name = devSupplierService.getNameById(supplierId);
        String supplierType = ZDYSupplierTypeEnum.getValueById(name);
        RestResponse response = new RestResponse();
        //String isGuojia = JudgeMeterType(supplierType, supplierId);
        String isGuojia="";
        if("云丁".equals(name)){
            isGuojia="4";
        }else if("蛋贝".equals(name)){
            isGuojia="3";
        }else if("广州翔羽".equals(name)){
            isGuojia="5";
        }
//
        switch (isGuojia) {
            case "0":
                response = meterFegin.authorization(code, supplierType, password, "", endDate);
                break;
            case "1":
                response = guojiaMeterFegin.authorization(code, password, startDate, endDate, tel);
                break;
//            case "2":
//                response = keluFegin.authorization(code, password, startDate, endDate);
//                break;
            case "3":
                response = danbayFegin.authorization(code, password, startDate, endDate);
                break;
            case "4":
                Map<String, Object> map = new HashMap<>();
                map.put("code", code);
                map.put("password", password);
                map.put("startDate", startDate);
                map.put("endDate", endDate);
                map.put("type","authorization");
                map.put("rentName",rentName);
                response = yundingFegin.action(map);
                break;
            case "5":
                Date beginTime = DateUtil.parseDateTime(startDate + " 00:00:00");
                Date endTime = DateUtil.parseDateTime(endDate + " 23:59:59");
                response = gzxyDoorLockFegin.apartmentAddPasswordKey(projectId,code,tel,password,beginTime,endTime);
                if(response.getSuccess()){
                    JSONObject res = JSONUtil.parseObj(response.get("data"));
                    response.put("password",res.getStr("password"));
                    response.put("passwordId",res.getStr("keyId"));
                }
                break;
            default:
                response.setSuccess(false).setMessage("门锁供应商判断有误");
                break;
        }

        return response;
    }

    @Override
    public RestResponse frozenResponse(String code, String supplierId, String pwdId, String projectId,String tel) throws Exception {
        //由原来的根据id获得supplierType，改成用name获得type。
        String name = devSupplierService.getNameById(supplierId);
        String supplierType = ZDYSupplierTypeEnum.getValueById(name);
        RestResponse response = new RestResponse();
        //String isGuojia = JudgeMeterType(supplierType, supplierId);
        String isGuojia="";
        if("云丁".equals(name)){
            isGuojia="4";
        }else if("蛋贝".equals(name)){
            isGuojia="3";
        }else if("广州翔羽".equals(name)){
            isGuojia="5";
        }
//        else{
//            return response.setSuccess(false).setMessage("对应的供应商无法操作");
//        }
        switch (isGuojia) {
            case "0":
                response = meterFegin.frozen(code, supplierType, pwdId, "");
                break;
            case "1":
                response = guojiaMeterFegin.frozen(code, pwdId);
                break;
//            case "2":
//                response = keluFegin.frozen(code, pwdId);
//                break;
            case "3":
                response = danbayFegin.frozen(code, pwdId);
                break;
            case "4":
                Map<String, Object> map = new HashMap<>();
                map.put("code", code);
                map.put("passwordId", pwdId);
                map.put("type","frozen");
                response = yundingFegin.action(map);
                break;
            case "5":
                response = gzxyDoorLockFegin.apartmentRoomFrozenTenant(projectId,code, tel);
                break;
            default:
                response.setSuccess(false).setMessage("门锁供应商判断有误");
                break;
        }
        return response;
    }

    private RestResponse deleteResponse(String code, String supplierId, String pwdId, String projectId) throws Exception {
        //由原来的根据id获得supplierType，改成用name获得type。
        String name = devSupplierService.getNameById(supplierId);
        String supplierType = ZDYSupplierTypeEnum.getValueById(name);
        RestResponse response = new RestResponse();
        String isGuojia="";
        if("云丁".equals(name)){
            isGuojia="4";
        }else if("蛋贝".equals(name)){
            isGuojia="3";
        }else if("广州翔羽".equals(name)){
            isGuojia="5";
        }else{
            return response.setSuccess(false).setMessage("对应的供应商无法操作");
        }
        switch (isGuojia) {
            case "0":
                response = meterFegin.frozen(code, supplierType, pwdId, "");
                break;
            case "1":
                response = guojiaMeterFegin.frozen(code, pwdId);
                break;
//            case "2":
//                response = keluFegin.frozen(code, pwdId);
//                break;
            case "3":
                response = danbayFegin.frozen(code, pwdId);
                break;
            case "4":
                Map<String, Object> map = new HashMap<>();
                map.put("code", code);
                map.put("passwordId", pwdId);
                map.put("type","deletePwd");
                response = yundingFegin.action(map);
                break;
            case "5":
                response = gzxyDoorLockFegin.apartmentRoomCheckOut(projectId,code);
                break;
            default:
                response.setSuccess(false).setMessage("门锁供应商判断有误");
                break;
        }
        return response;
    }

    @Override
    public RestResponse unFrozenResponse(String code, String supplierId, String pwdId, String projectId,String tel) throws Exception {
        //由原来的根据id获得supplierType，改成用name获得type。
        RestResponse response = new RestResponse();
        String name = devSupplierService.getNameById(supplierId);
        String supplierType = ZDYSupplierTypeEnum.getValueById(name);
        String isGuojia="";
        if("云丁".equals(name)){
            isGuojia="4";
        }else if("蛋贝".equals(name)){
            isGuojia="3";
        }else if("广州翔羽".equals(name)){
            isGuojia="5";
        }
        switch (isGuojia) {
            case "0":
                response = meterFegin.frozen(code, supplierType, pwdId, "");
                break;
            case "4":
                Map<String, Object> map = new HashMap<>();
                map.put("code", code);
                map.put("passwordId", pwdId);
                map.put("type","unfrozen");
                response = yundingFegin.action(map);
                break;
            case "5":
                response = gzxyDoorLockFegin.apartmentRoomUnFrozenTenant(projectId,code,tel);
                break;
            default:
                response.setSuccess(false).setMessage("门锁供应商判断有误");
                break;
        }
        return response;
    }

    @Override
    public RestResponse getReadResponse(String code, String supplierId, Boolean isWater) throws Exception {
        //由原来的根据id获得supplierType，改成用name获得type。

        String name = devSupplierService.getNameById(supplierId).trim();
        log.info("供应商名字："+name);
        String supplierType = ZDYSupplierTypeEnum.getValueById(name);
        log.info("供应商名字："+supplierType);
        //String meterType = JudgeMeterType(supplierType, supplierId);
        String meterType="";
        RestResponse response = null;
        if("云丁".equals(name)){
            meterType="4";
        }else if("蛋贝".equals(name)){
            meterType="3";
        }else if("拓强".equals(name)){
            meterType="5";
        }else if("无忧抄表".equals(name)){
            meterType="6";
        }
//        else{
//            return response.setSuccess(false).setMessage("对应的供应商无法操作");
//        }

        switch (meterType) {
            case "0":
                response = meterFegin.getRead(isWater, code, supplierType);
                break;
            case "1":
                response = guojiaMeterFegin.getRead(code);
                break;
            case "2":
                response = keluFegin.meterInfo(code);
                break;
            case "3":
                response = danbayFegin.getRead(code);
                break;
            case "4":
                Map<String, Object> map = new HashMap<>();
                map.put("code", code);
                if (isWater) {
                    map.put("type","getWaterRead");
                    response = yundingFegin.action(map);
                } else {
                    map.put("type","getEleRead");
                    response = yundingFegin.action(map);
                }
                break;
            case "5":
                Map<String, Object> map5 = new HashMap<>();
                map5.put("deviceCode", code);
                map5.put("readTime", DateUtil.formatDate(new Date()));
                List<DevDeviceLogEntity> reads = devDeviceLogService.getRecordsByMap(map5);
                response = new RestResponse();
                if(reads!=null&&reads.size()>0){
                    response.put("read", reads.get(0).getReadNum().toString());
                    response.setSuccess(true);
                }else{
                    response.setSuccess(false);
                }
                break;
            case "6":
                Map<String, Object> map6 = new HashMap<>();
                map6.put("deviceCode", code);
                map6.put("readTime", DateUtil.formatDate(new Date()));
                List<DevDeviceLogEntity> reads2 = devDeviceLogService.getRecordsByMap(map6);
                response = new RestResponse();
                if(reads2!=null&&reads2.size()>0){
                    response.put("read", reads2.get(0).getReadNum().toString());
                    response.setSuccess(true);
                }else{
                    response.setSuccess(false);
                }
                break;
        }
        return response;
    }

    @Override
    public RestResponse getTempPasswordResponse(String code, String supplierId, String projectId) throws Exception {
        //由原来的根据id获得supplierType，改成用name获得type。
        String name = devSupplierService.getNameById(supplierId);
        String supplierType = ZDYSupplierTypeEnum.getValueById(name);
        RestResponse response = new RestResponse();
        //String isGuojia = JudgeMeterType(supplierType, supplierId);
        String isGuojia="";
        if("云丁".equals(name)){
            isGuojia="4";
        }else if("蛋贝".equals(name)){
            isGuojia="3";
        }else if("广州翔羽".equals(name)){
            isGuojia="5";
        }
//        else{
//            return response.setSuccess(false).setMessage("对应的供应商无法操作");
//        }
        switch (isGuojia) {
            case "0":
                response = meterFegin.getTempPassword(code, supplierType);
                break;
            case "1":
                response = guojiaMeterFegin.getTempPassword(code, "");
                break;
//            case "2":
//                response = keluFegin.getTempPassword(code);
//                break;
            case "3":
                response = danbayFegin.getTempPassword(code);
                break;
            case "4":
                Map<String, Object> map = new HashMap<>();
                map.put("code", code);
                map.put("type","getTempPassword");
                response = yundingFegin.action(map);
                break;
            case "5":
                response = gzxyDoorLockFegin.apartmentAddSingleKey(projectId,code);
                JSONObject res = JSONUtil.parseObj(response.get("data"));
                response.put("password",res.getStr("password"));
                response.put("passwordId",res.getStr("keyId"));
                break;
            default:
                response.setSuccess(false).setMessage("门锁供应商判断有误");
                break;
        }
        return response;
    }

    @Override
    public RestResponse openCloseResponse(String code, String supplierId, String action, Boolean isWater) throws Exception {
        //由原来的根据id获得supplierType，改成用name获得type。
        String name = devSupplierService.getNameById(supplierId);
        String supplierType = ZDYSupplierTypeEnum.getValueById(name);
        RestResponse response = new RestResponse();
        DevDeviceEntity devDeviceEntity = getByCode(code);
        //String isGuojia = JudgeMeterType(supplierType, supplierId);
        String isGuojia="";
        if("云丁".equals(name)){
            isGuojia="4";
        }else if("蛋贝".equals(name)){
            isGuojia="3";
        }else if("拓强".equals(name)){
            isGuojia="5";
        }else if("无忧抄表".equals(name)){
            isGuojia="6";
        }
//        else{
//            return response.setSuccess(false).setMessage("对应的供应商无法操作");
//        }
        switch (isGuojia) {
            case "0":
                response = "0".equals(action) ? meterFegin.meterOpen(isWater, code, supplierType) : meterFegin.meterClose(isWater, code, supplierType);
                break;
            case "1":
                response = guojiaMeterFegin.openClose(code, action);
                break;
            case "2":
//                if ("0".equals(action)) {
//                    action = "1";
//                } else {
//                    action = "0";
//                }
                //response = keluFegin.ctrl(code, action);

                response.setSuccess(false).setMessage("科陆智能电表不支持通电、断电功能");
                break;
            case "3":
                response = danbayFegin.openClose(code, action);
                break;
            case "4":
                Map<String, Object> map = new HashMap<>();
                map.put("code", code);
                map.put("type","openCloseEle");
                map.put("action",Integer.valueOf(action));
                response = yundingFegin.action(map);
                break;
            case "5":
                response = "1".equals(action) ? tqDianbiaoFegin.eleOpen("HTGY",devDeviceEntity.getPurchaseDetailId(),code) :tqDianbiaoFegin.eleClose("HTGY",devDeviceEntity.getPurchaseDetailId(),code);
                break;
            case "6":
                response = "1".equals(action) ? ykWaterFegin.meterOpen("HTGY",code) : ykWaterFegin.meterClose("HTGY",code);
                break;
        }
        return response;
    }

    @Override
    public RestResponse getWaterRecords(String code, String beginDate, String endDate) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("code", code);
        map.put("beginDate",beginDate);
        map.put("endDate",endDate);
        map.put("type","getWaterRecords");
        RestResponse response = yundingFegin.action(map);
        return response;
    }

    @Override
    public RestResponse getEleRecords(String code, String beginDate, String endDate,String sourceId) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("code", code);
        map.put("beginDate",beginDate);
        map.put("endDate",endDate);
        map.put("sourceId",sourceId);
        map.put("type","getEleRecords");
        RestResponse response = yundingFegin.action(map);
        return response;
    }

    @Override
    public RestResponse getLockRecords(String code,String supplierId, String beginDate, String endDate) throws Exception {
        //由原来的根据id获得supplierType，改成用name获得type。
        String name = devSupplierService.getNameById(supplierId);
        String supplierType = ZDYSupplierTypeEnum.getValueById(name);
        RestResponse response = new RestResponse();
        //String meterType = JudgeMeterType(supplierType, supplierId);
        String meterType="";
        if("云丁".equals(name)){
            meterType="4";
        }else if("蛋贝".equals(name)){
            meterType="3";
        }
//        else{
//            return response.setSuccess(false).setMessage("对应的供应商无法操作");
//        }
        switch (meterType) {
            case "3":
                response = danbayFegin.getLockRecords(code,beginDate,endDate);
                break;
            case "4":
                Map<String, Object> map = new HashMap<>();
                map.put("code", code);
                map.put("beginDate",beginDate);
                map.put("endDate",endDate);
                map.put("type","getLockRecords");
                response = yundingFegin.action(map);
                break;
        }

        return response;
    }

    @Override
    public RestResponse getSuperPassowrd(String code, String sourceId)throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("code", code);
        map.put("sourceId",sourceId);
        map.put("type","getSuperPassowrd");
        RestResponse response = yundingFegin.action( map);
        return response;
    }

//    @Override
//    public List<Map<String,List>> getIdByRenter(String renterId,String typeId) {
//       return deviceDao.getId(renterId,typeId);
//    }

    @Override
    public List<ResSourceDeviceInfo> getIdByRenter(String renterId, String typeId) {
        return deviceDao.getId(renterId,typeId);
    }

    /*
      修改在线密码
     */
    @Override
    public RestResponse updatePassowrd(Map<String,Object> map) throws Exception {
        String name = devSupplierService.getNameById(map.get("supplierId").toString());
        RestResponse response = new RestResponse();
        String isGuojia="";
        if("云丁".equals(name)){
            isGuojia="4";
        }else if("广州翔羽".equals(name)){
            isGuojia="5";
        }
        switch (isGuojia) {
            case "4":
                map.put("type","update");
                response = yundingFegin.action(map);
                break;
            case "5":
                String projectId = map.get("projectId").toString();
                String keyId = map.get("passwordId").toString();
                String roomPassword = map.get("password").toString();
                response = gzxyDoorLockFegin.apartmentModifyKey(projectId,keyId,roomPassword);
                break;
            default:
                response.setSuccess(false).setMessage("门锁供应商判断有误");
                break;
        }
        return response;
    }

    @Override
    public RestResponse delFrozen(String sourceId, String renterId) throws Exception {
        if (StrUtil.isBlank(sourceId)) {
            return RestResponse.failure("密码删除失败,sourceId参数为空");
        }
        if (StrUtil.isBlank(renterId)) {
            return RestResponse.failure("密码删除失败,renterId参数为空");
        }
        //先查出该房源下的所有门锁
        Map<String, Object> menjinMap = Maps.newHashMap();
        menjinMap.put("sourceId", sourceId);
        menjinMap.put("name", "智能门锁");
        menjinMap.put("type", "0");
        List<DevDeviceEntityVo> deviceList = this.queryDevByNameAndSourid(menjinMap);
        if (deviceList.size() > 0) {
            for (DevDeviceEntityVo deviceEntityVo : deviceList) {
                //查出租客在该门锁的授权记录(锁有效时间为空的数据在前面)
                DevLockPasswordEntity lockPasswordEntity = devLockPasswordService.queryNullFirst(deviceEntityVo.getId(), renterId);
                //有授权记录的话就对该密码进行冻结
                if (ObjectUtil.isNotNull(lockPasswordEntity)) {
                    //ResSourceVo sourceVo = resSourceService.getInfoById(sourceId);
                    //String sourceName = sourceVo.getHouseName();
                    RestResponse response = deleteResponse(deviceEntityVo.getCode(), deviceEntityVo.getSupplierId(), lockPasswordEntity.getPasswordId(), deviceEntityVo.getProjectId());
                    if ((boolean) response.get("success") || "密码已经过期".equals(response.getMessage()) || "密码有效期已过，不支持该操作".equals(response.getMessage())) {
                        lockPasswordEntity.deleteById();
                    } else {
                        return RestResponse.failure("门锁删除失败,原因：" + response.getMessage());
                    }
                }
            }
        }
        return RestResponse.success();
    }

    public Map<String, Object> countDevsByCityCode(String cityCode){
        return this.baseMapper.countDevsByCityCode(cityCode);
    }

    @Override
    public RestResponse forceOpenResponse(String code, String supplierId, int openState) throws Exception {
        //由原来的根据id获得supplierType，改成用name获得type。
        String name = devSupplierService.getNameById(supplierId);
        RestResponse response = new RestResponse();
        if("云丁".equals(name)){
            Map<String, Object> map = new HashMap<>();
            map.put("code", code);
            map.put("type","openCloseEle");
            map.put("openState",openState);
            response = yundingFegin.action(map);
        }
        return response;
    }

    @Override
    public IPage<DevDeviceEntityVo> getDeviceListPage(Page page, DevDeviceEntitySerchVo serchVo) {
        serchVo.setProjectId(UoneHeaderUtil.getProjectId());
        IPage<DevDeviceEntityVo> IPage = baseMapper.getDevicesByParams(page,serchVo);
        return IPage;
    }

    @Override
    public List<DevDeviceEntityVo> getDeviceList(DevDeviceEntitySerchVo serchVo) {
        serchVo.setProjectId(UoneHeaderUtil.getProjectId());
        List<DevDeviceEntityVo> list = baseMapper.getDevicesByParams(serchVo);
        return list;
    }

}
