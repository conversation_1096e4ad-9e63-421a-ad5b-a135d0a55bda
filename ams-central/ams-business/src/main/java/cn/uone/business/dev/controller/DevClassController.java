package cn.uone.business.dev.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.dev.DevClassEntity;
import cn.uone.bean.entity.business.dev.DevDeviceEntity;
import cn.uone.bean.entity.business.dev.DevPropertyDeviceEntity;
import cn.uone.business.dev.service.IDevAuthorizationService;
import cn.uone.business.dev.service.IDevClassService;
import cn.uone.business.dev.service.IDevDeviceService;
import cn.uone.business.dev.service.IDevPropertyDeviceService;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.annotation.CacheLock;
import cn.uone.web.base.annotation.CacheParam;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
@Api(value="设备类型服务",tags={"设备类型操作"})
@RestController
@RequestMapping("/dev-class-entity")
public class DevClassController extends BaseController  {

    @Autowired
    private IDevClassService devClassService;
    @Autowired
    private IDevPropertyDeviceService devPropertyDeviceService;
    @Autowired
    private IDevDeviceService devDeviceService;
    @Autowired
    private IDevAuthorizationService devAuthorizationService;


    @ApiOperation(value = "设备菜单列表")
    @RequestMapping("/queryDevClassMenu")
    public RestResponse queryDevClassMenu(@RequestParam(name = "type",required = false) String type,@RequestParam(name="parent",required = false)String parent,@RequestParam(value = "id",required = false)String id,@RequestParam(value = "child",required = false)String child) {
        if (StringUtils.isBlank(type)){
            return RestResponse.failure("类型不能为空要不无法区分是物业设备还是采购设备");
        }
        QueryWrapper<DevClassEntity> wrapper=new QueryWrapper();
        wrapper.eq("type",type);
        if(StringUtils.isBlank(child)){//查询父id条件下的设备
            if (StringUtils.isNotBlank(parent) && StringUtils.isBlank(id)){//如果parent不为空且id为空就只查询父菜单
                wrapper.and(i->i.isNull("parent_id").or().eq("parent_id",""));
            }
            if (StringUtils.isNotBlank(parent) && StringUtils.isNotBlank(id)){//如果都不为空查询子菜单
                wrapper.eq("parent_id",id);
            }else {
                if(StringUtils.isNotBlank(id)){//查询父菜单带子菜单
                    wrapper.and(i->i.eq("id",id).or().eq("parent_id",id));
                }
            }
        }else{
            wrapper.isNotNull("parent_id");//查询所有物业设备的子类
        }
        List<DevClassEntity> list =devClassService.list(wrapper);
        return RestResponse.success().setData(list);
    }

    @ApiOperation(value = "分页查询设备菜单")
    @PostMapping("/queryDevClassMenuPage")
    public RestResponse queryDevClassMenuPage(@RequestParam(name = "id",required = false) String id, @RequestParam(name = "size",required = false) Integer size, @RequestParam(value = "current",required = false) Integer current, @RequestParam(value = "type",required = false) String type) {
        if (StringUtils.isBlank(type)){
            return RestResponse.failure("类型不能为空要不无法区分是物业设备还是采购设备");
        }
        Map map=new HashMap();
        if(StringUtils.isNotBlank(id)){
            map.put("id",id);
        }
        if(size!=null && current!=null){
            map.put("pageSize",size);
            map.put("pageIndex",current);
        }
        map.put("type",type);
        IPage<DevClassEntity> pages= devClassService.queryDevClassMenuPage(map);
        RestResponse restResponse=new RestResponse();
        restResponse.setData(pages);
        return restResponse;
    }

    @ApiOperation(value = "添加设备分类")
    @PostMapping("/addDevClass")
    @CacheLock(prefix = "addDevClass")
    public RestResponse addDevClass(@CacheParam @RequestBody DevClassEntity devClassEntity) {
        String str=checkFun(devClassEntity);
        if (StringUtils.isNotBlank(str)){
            return RestResponse.failure(str);
        }
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("name", devClassEntity.getName());
        DevClassEntity entity = devClassService.getOne(wrapper);
        if (ObjectUtil.isNotNull(entity)) {
            return RestResponse.failure("该设备分类名称已经存在");
        }
        devClassService.addDevClass(devClassEntity);

        return RestResponse.success("保存成功");
    }

    @ApiOperation(value = "设备分类修改")
    @PostMapping("/updateDevClass")
    @CacheLock(prefix = "updateDevClass")
    public RestResponse updateDevClass(@CacheParam @RequestBody DevClassEntity devClassEntity) {
        String str=checkFun(devClassEntity);
        if (StringUtils.isNotBlank(str)){
            return RestResponse.failure(str);
        }
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("name", devClassEntity.getName());
        if (StrUtil.isNotBlank(devClassEntity.getId())) {
            wrapper.ne("id", devClassEntity.getId());
        }
        DevClassEntity entity = devClassService.getOne(wrapper);
        if (ObjectUtil.isNotNull(entity)) {
            return RestResponse.failure("该设备分类名称已经存在");
        }
        if (StringUtils.isBlank(devClassEntity.getId())){
            return  RestResponse.failure("设备Id不能为空");
        }
        devClassService.updateDevClass(devClassEntity);
        return RestResponse.success("保存成功");
    }

    @ApiOperation(value = "设备分类删除")
    @PostMapping("/delDevClass")
    @CacheLock(prefix = "delDevClass")
    public RestResponse delDevClass(@CacheParam(name = "id") @RequestParam(value = "id",required = false) String id) {
        if (StringUtils.isBlank(id)){
            return  RestResponse.failure("设备Id不能为空");
        }
        QueryWrapper wrapper=new QueryWrapper();
        wrapper.eq("class_id",id);
        List<DevPropertyDeviceEntity> list= devPropertyDeviceService.list(wrapper);
        if (!CollectionUtils.isEmpty(list)){
            return  RestResponse.failure("该设备分类已关联到设备，不能删除！");
        }
        DevDeviceEntity devDeviceEntity=new DevDeviceEntity();
        devDeviceEntity.setClassId(id);
        List<DevDeviceEntity> ddsList=devDeviceService.queryDevDevice(devDeviceEntity);
        if (!CollectionUtils.isEmpty(ddsList)){
            return  RestResponse.failure("该设备分类已关联到设备，不能删除！");
        }
        devClassService.delDevClass(id);
        return RestResponse.success("删除成功");
    }

    public String checkFun(DevClassEntity devClassEntity){
        String str="";
        if (devClassEntity==null){
             return "请求对象为空";
        }
        if(StringUtils.isBlank(devClassEntity.getParentId())){
            str="所属系统不能为空";
        }
        if (StringUtils.isBlank(devClassEntity.getName())){
            str="类别名称不能为空";
        }
        if (StringUtils.isBlank(devClassEntity.getType())){
            str="类型不能为空要不无法区分是物业设备还是采购设备";
        }
        return str;
    }

    @RequestMapping("/queryDevClassToLogSelect")
    public RestResponse queryDevClassToLogSelect() {
        QueryWrapper<DevClassEntity> wrapper = new QueryWrapper();
        wrapper.eq("name", "智能水表")
                .or().eq("name", "智能电表")
                .or().eq("name", "燃气表")
                .or().eq("name", "普通水表")
                .or().eq("name", "普通电表");
        List<DevClassEntity> list = devClassService.list(wrapper);
        return RestResponse.success().setData(list);
    }
}
