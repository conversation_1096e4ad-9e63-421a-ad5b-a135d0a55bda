package cn.uone.business.base.service;

import cn.uone.bean.entity.business.base.BaseBankEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-12
 */
public interface IBaseBankService extends IService<BaseBankEntity> {

    List<BaseBankEntity> findByCondition();

    List<BaseBankEntity> selectBankList();
}
