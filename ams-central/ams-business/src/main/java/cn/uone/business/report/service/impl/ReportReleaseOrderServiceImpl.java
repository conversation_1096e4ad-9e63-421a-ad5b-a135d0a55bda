package cn.uone.business.report.service.impl;

import cn.uone.bean.entity.business.report.ReportReleaseOrderEntity;
import cn.uone.bean.entity.business.report.vo.*;
import cn.uone.bean.parameter.ConfirmReleasePo;
import cn.uone.business.report.dao.ReportReleaseOrderDao;
import cn.uone.business.report.service.IReportReleaseOrderService;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-06
 */
@Service
public class ReportReleaseOrderServiceImpl extends ServiceImpl<ReportReleaseOrderDao, ReportReleaseOrderEntity> implements IReportReleaseOrderService {

    @Override
    public List<ConfirmReleaseVo> report(ConfirmReleasePo param) {
        return baseMapper.report(param);
    }

    @Override
    public IPage<ConfirmReleaseVo> reportPage(Page page, ConfirmReleasePo param) {
        return baseMapper.report(page,param);
    }

    @Override
    public IPage<ReceivableVo> getReceivableList(Page page, ReceivableVo receivableVo) {
        receivableVo.setProjectId(UoneHeaderUtil.getProjectId());
        return baseMapper.getReceivableList(page,receivableVo);
    }

    @Override
    public String getTotalMoney(ReceivableVo receivableVo) {
        receivableVo.setProjectId(UoneHeaderUtil.getProjectId());
        return baseMapper.getTotalMoney(receivableVo);
    }

    @Override
    public List<ReceivableVo> getList(ReceivableVo receivableVo) {
        receivableVo.setProjectId(UoneHeaderUtil.getProjectId());
        return baseMapper.getReceivableList(receivableVo);
    }

    @Override
    public IPage<BillDetailVo> getBillDetail(Page page, BillDetailVo vo) {
        vo.setProjectId(UoneHeaderUtil.getProjectId());
        return baseMapper.getBillDetail(page,vo);
    }

    @Override
    public List<BillDetailVo> getBillDetail(BillDetailVo  vo) {
        vo.setProjectId(UoneHeaderUtil.getProjectId());
        return baseMapper.getBillDetail(vo);
    }

    @Override
    public IPage<DepositEarnestVo> getDepositEarnest(Page page,DepositEarnestVo vo) {
        return baseMapper.getDepositEarnest(page,vo);
    }

    @Override
    public IPage<IncomeCarryforwardVo> getIncomeCarryforward(Page page,IncomeCarryforwardVo vo) {
        return baseMapper.getIncomeCarryforward(page,vo);
    }

    @Override
    public IPage<ArrearageVo> getArrearage(Page page, ArrearageVo vo) {
        return baseMapper.getArrearage(page,vo);
    }

    @Override
    public IPage<RentalIncomeVo> getRentalIncome(Page page, RentalIncomeVo vo) {
        return baseMapper.getRentalIncome(page,vo);
    }

    @Override
    public IPage<RentalIncomeVo> getRentalIncomeForZzct(Page page, RentalIncomeVo vo) {
        return baseMapper.getRentalIncomeForZzct(page,vo);
    }


}
