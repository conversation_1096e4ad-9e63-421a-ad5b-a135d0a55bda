package cn.uone.business.cont.task;

import cn.uone.bean.entity.business.cont.ContContractEntity;
import org.springframework.context.ApplicationEvent;

public class AuditContractTransactionEvent extends ApplicationEvent {

    private ContContractEntity contract;

    private String createRentType;


    public AuditContractTransactionEvent(Object source, ContContractEntity contract,String createRentType) {
        super(source);
        this.contract = contract;
        this.createRentType = createRentType;
    }

    public ContContractEntity getContract() {
        return contract;
    }

    public String getCreateRentType() {
        return createRentType;
    }
}
