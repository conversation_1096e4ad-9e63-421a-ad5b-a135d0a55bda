package cn.uone.business.investment.controller;


import cn.hutool.core.util.StrUtil;
import cn.uone.bean.entity.business.investment.InvestmentTokerEntity;
import cn.uone.bean.entity.crm.UserEntity;
import cn.uone.business.investment.service.IInvestmentTokerService;
import cn.uone.fegin.crm.IUserFegin;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.web.base.BaseController;
import cn.uone.web.base.RestResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 拓客信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-26
 */
@RestController
@RequestMapping("/investment/toker")
public class InvestmentTokerController extends BaseController {

    @Autowired
    private IInvestmentTokerService service;


    @GetMapping("/page")
    public RestResponse page(Page<InvestmentTokerEntity> page, InvestmentTokerEntity entity){
        QueryWrapper<InvestmentTokerEntity> wrapper = new QueryWrapper<>();

        if(StrUtil.isNotBlank(entity.getChannel())){
            wrapper.like("channel","%"+entity.getChannel()+"%");
        }
        if(StrUtil.isNotBlank(entity.getBrand())){
            wrapper.like("brand","%"+entity.getBrand()+"%");
        }
        if(StrUtil.isNotBlank(entity.getStatus())){
            wrapper.eq("status",entity.getStatus());
        }
        if(StrUtil.isNotBlank(entity.getIntentionLevel())){
            wrapper.eq("intention_level",entity.getIntentionLevel());
        }
        if(StrUtil.isNotBlank(entity.getTracker())){
            wrapper.like("tracker ","%"+entity.getTracker()+"%");
        }
        wrapper.orderByDesc("create_date");
        IPage<InvestmentTokerEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }
    @GetMapping("/clientele")
    public RestResponse clientele(Page<InvestmentTokerEntity> page, InvestmentTokerEntity entity){
        QueryWrapper<InvestmentTokerEntity> wrapper = new QueryWrapper<>();
        if(StrUtil.isNotBlank(entity.getChannel())){
            wrapper.like("channel","%"+entity.getChannel()+"%");
        }
        if(StrUtil.isNotBlank(entity.getBrand())){
            wrapper.like("brand","%"+entity.getBrand()+"%");
        }
        if(StrUtil.isNotBlank(entity.getStatus())){
            wrapper.eq("status",entity.getStatus());
        }
        if(StrUtil.isNotBlank(entity.getIntentionLevel())){
            wrapper.eq("intention_level",entity.getIntentionLevel());
        }
        if(StrUtil.isNotBlank(entity.getTracker())){
            wrapper.like("tracker ","%"+entity.getTracker()+"%");
        }
        wrapper.eq("tracker_id",UoneSysUser.id());
        wrapper.eq("status","2");
        wrapper.orderByDesc("create_date");
        IPage<InvestmentTokerEntity> p = service.page(page,wrapper);
        return RestResponse.success().setData(p);
    }

    @PostMapping("/save")
    public RestResponse save(InvestmentTokerEntity entity){
        entity.setStatus("1");
        entity.insertOrUpdate();
        return RestResponse.success();
    }

    @PostMapping("/edit")
    public RestResponse edit(InvestmentTokerEntity entity){
        entity.updateById();
        return RestResponse.success();
    }

    @PostMapping("/remove")
    public RestResponse remove(@RequestBody List<String> ids){
        service.removeByIds(ids);
        return RestResponse.success();
    }

}
