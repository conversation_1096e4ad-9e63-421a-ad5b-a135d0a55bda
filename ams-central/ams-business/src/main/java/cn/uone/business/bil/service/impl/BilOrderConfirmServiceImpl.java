package cn.uone.business.bil.service.impl;

import cn.uone.bean.entity.business.bil.BilOrderConfirmEntity;
import cn.uone.business.bil.dao.BilOrderConfirmDao;
import cn.uone.business.bil.service.IBilOrderConfirmService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 退款/收款确认表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
@Service
public class BilOrderConfirmServiceImpl extends ServiceImpl<BilOrderConfirmDao, BilOrderConfirmEntity> implements IBilOrderConfirmService {

    @Override
    public IPage<BilOrderConfirmEntity> page(Page<BilOrderConfirmEntity> page, BilOrderConfirmEntity p,String confirmIds) {
        return baseMapper.page(page, p,confirmIds);
    }
    @Override
    public IPage<BilOrderConfirmEntity> page(Page<BilOrderConfirmEntity> page, BilOrderConfirmEntity p) {
        return baseMapper.page(page, p);
    }
    @Override
    public List<BilOrderConfirmEntity> getIdList() {
        return baseMapper.getIdList();
    }


}
