package cn.uone.business.bil.controller;

import cn.uone.application.enumerate.order.InvoiceStateEnum;
import cn.uone.bean.base.response.RestResponse;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.invoice.vo.InvoiceBuyerVo;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.rpt.service.IReportInvoiceService;
import cn.uone.web.base.annotation.CacheLock;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.*;

/**
 * BilOrderController集成测试类
 * 
 * 此测试类主要验证在真实环境中开票流程的正确性和并发控制机制
 * 包括：
 * 1. 单个订单开票流程的完整性测试
 * 2. 并发开票请求的防重复机制测试
 * 3. 高压力下的系统稳定性测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("test") // 使用测试环境配置
public class BilOrderControllerIntegrationTest {

    @Autowired
    private BilOrderController bilOrderController;

    @Autowired
    private IBilOrderService bilOrderService;

    @Autowired
    private IReportInvoiceService reportInvoiceService;

    /**
     * 准备测试数据 - 创建一个未开票状态的订单
     * 注意：实际集成测试中，可能需要通过SQL脚本或API先创建订单
     */
    private BilOrderEntity prepareTestOrder() {
        // 在实际集成测试中，这里应该创建真实的订单记录
        // 以下代码仅为示例，实际项目中需要替换为真实的订单创建逻辑
        BilOrderEntity order = new BilOrderEntity();
        order.setCode("TEST-" + UUID.randomUUID().toString().substring(0, 8));
        order.setInvoiceState(InvoiceStateEnum.UNBILLED.getValue());
        
        // 保存订单并返回
        bilOrderService.save(order);
        return order;
    }

    /**
     * 测试1：基础开票流程集成测试
     * 验证在真实环境中完整的开票流程
     */
    @Test
    @Transactional // 使用事务，测试完成后回滚
    @Sql("/sql/prepare-test-order.sql") // 预先通过SQL脚本准备测试数据
    public void testBasicInvoiceProcess() {
        // 获取测试订单ID (假设SQL脚本中创建的订单ID为TEST_ORDER_ID)
        String testOrderId = "TEST_ORDER_ID";
        
        // 查询订单确认初始状态
        BilOrderEntity initialOrder = bilOrderService.getById(testOrderId);
        assertEquals("初始状态应为未开票", 
                InvoiceStateEnum.UNBILLED.getValue(), 
                initialOrder.getInvoiceState());
        
        // 创建开票请求
        InvoiceBuyerVo invoiceRequest = new InvoiceBuyerVo();
        invoiceRequest.setOrderId(testOrderId);
        invoiceRequest.setInvoiceTypeCode("REPORT");
        
        // 执行开票请求
        RestResponse response = bilOrderController.makeInvoice(invoiceRequest);
        
        // 验证响应
        assertTrue("开票请求应成功", response.getSuccess());
        
        // 验证订单状态已更新
        BilOrderEntity updatedOrder = bilOrderService.getById(testOrderId);
        assertEquals("开票后状态应更新为已开票", 
                InvoiceStateEnum.INVOICED.getValue(), 
                updatedOrder.getInvoiceState());
    }

    /**
     * 测试2：重复开票防护集成测试
     * 验证对同一订单的重复开票请求被正确拒绝
     */
    @Test
    @Transactional
    @Sql("/sql/prepare-test-order.sql")
    public void testDuplicateInvoiceProtection() {
        // 获取测试订单ID
        String testOrderId = "TEST_ORDER_ID";
        
        // 创建开票请求
        InvoiceBuyerVo invoiceRequest = new InvoiceBuyerVo();
        invoiceRequest.setOrderId(testOrderId);
        invoiceRequest.setInvoiceTypeCode("REPORT");
        
        // 第一次开票请求
        RestResponse firstResponse = bilOrderController.makeInvoice(invoiceRequest);
        assertTrue("第一次开票请求应成功", firstResponse.getSuccess());
        
        // 第二次开票请求
        RestResponse secondResponse = bilOrderController.makeInvoice(invoiceRequest);
        assertFalse("第二次开票请求应被拒绝", secondResponse.getSuccess());
        assertTrue("错误消息应包含状态提示", 
                secondResponse.getMessage().contains("不允许重复开票"));
    }

    /**
     * 测试3：并发开票请求集成测试
     * 验证在真实环境中，并发请求时@CacheLock机制是否正常工作
     */
    @Test
    @Transactional
    @Sql("/sql/prepare-test-order.sql")
    public void testConcurrentInvoiceRequests() throws Exception {
        // 配置并发测试参数
        final int THREAD_COUNT = 10;
        final CountDownLatch startLatch = new CountDownLatch(1);
        final CountDownLatch endLatch = new CountDownLatch(THREAD_COUNT);
        final AtomicInteger successCount = new AtomicInteger(0);
        final List<RestResponse> responses = new ArrayList<>();
        
        // 获取测试订单ID
        String testOrderId = "TEST_ORDER_ID";
        
        // 创建并发测试线程池
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_COUNT);
        
        for (int i = 0; i < THREAD_COUNT; i++) {
            executor.submit(() -> {
                try {
                    // 等待所有线程准备好同时开始
                    startLatch.await();
                    
                    // 创建开票请求
                    InvoiceBuyerVo request = new InvoiceBuyerVo();
                    request.setOrderId(testOrderId);
                    request.setInvoiceTypeCode("REPORT");
                    
                    // 执行开票请求
                    RestResponse response = bilOrderController.makeInvoice(request);
                    responses.add(response);
                    
                    // 统计成功请求
                    if (response.getSuccess()) {
                        successCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    endLatch.countDown();
                }
            });
        }
        
        // 同时触发所有线程开始执行
        startLatch.countDown();
        
        // 等待所有线程执行完成
        endLatch.await(30, TimeUnit.SECONDS);
        executor.shutdown();
        
        // 验证结果
        assertEquals("并发请求中应只有一个成功", 1, successCount.get());
        
        // 验证最终订单状态
        BilOrderEntity finalOrder = bilOrderService.getById(testOrderId);
        assertEquals("最终状态应为已开票", 
                InvoiceStateEnum.INVOICED.getValue(), 
                finalOrder.getInvoiceState());
    }

    /**
     * 测试4：压力测试 - 模拟高并发场景
     * 验证系统在高压力下的稳定性和正确性
     */
    @Test
    @Transactional
    public void testHighConcurrencyStressTest() throws Exception {
        // 配置压力测试参数
        final int ORDER_COUNT = 5; // 测试订单数量
        final int THREADS_PER_ORDER = 20; // 每个订单的并发线程数
        final int TOTAL_THREADS = ORDER_COUNT * THREADS_PER_ORDER;
        
        final CountDownLatch startLatch = new CountDownLatch(1);
        final CountDownLatch endLatch = new CountDownLatch(TOTAL_THREADS);
        final AtomicInteger successCount = new AtomicInteger(0);
        
        // 创建测试订单
        List<BilOrderEntity> testOrders = new ArrayList<>();
        for (int i = 0; i < ORDER_COUNT; i++) {
            BilOrderEntity order = prepareTestOrder();
            testOrders.add(order);
        }
        
        // 创建并发测试线程池
        ExecutorService executor = Executors.newFixedThreadPool(TOTAL_THREADS);
        
        // 为每个订单创建多个并发请求
        for (BilOrderEntity order : testOrders) {
            for (int i = 0; i < THREADS_PER_ORDER; i++) {
                executor.submit(() -> {
                    try {
                        // 等待所有线程准备好同时开始
                        startLatch.await();
                        
                        // 创建开票请求
                        InvoiceBuyerVo request = new InvoiceBuyerVo();
                        request.setOrderId(order.getId());
                        request.setInvoiceTypeCode("REPORT");
                        
                        // 执行开票请求
                        RestResponse response = bilOrderController.makeInvoice(request);
                        
                        // 统计成功请求
                        if (response.getSuccess()) {
                            successCount.incrementAndGet();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        endLatch.countDown();
                    }
                });
            }
        }
        
        // 同时触发所有线程开始执行
        startLatch.countDown();
        
        // 等待所有线程执行完成，设置较长的超时时间
        boolean completed = endLatch.await(60, TimeUnit.SECONDS);
        executor.shutdown();
        
        // 验证结果
        assertTrue("所有线程应在超时前完成", completed);
        assertEquals("每个订单应只成功开一次票", ORDER_COUNT, successCount.get());
        
        // 验证所有订单最终状态
        for (BilOrderEntity order : testOrders) {
            BilOrderEntity finalOrder = bilOrderService.getById(order.getId());
            assertEquals("订单最终状态应为已开票", 
                    InvoiceStateEnum.INVOICED.getValue(), 
                    finalOrder.getInvoiceState());
        }
    }

    /**
     * 测试5：状态回滚机制集成测试
     * 模拟开票服务失败场景，验证状态能否正确回滚
     * 注意：此测试需要与真实服务集成，或者使用模拟服务
     */
    @Test
    @Transactional
    public void testInvoiceFailureRecovery() {
        // 创建一个用于测试的订单
        BilOrderEntity testOrder = prepareTestOrder();
        
        // 创建特殊的开票请求，触发开票失败
        // 此处假设有一个特殊的发票类型会导致开票失败
        InvoiceBuyerVo request = new InvoiceBuyerVo();
        request.setOrderId(testOrder.getId());
        request.setInvoiceTypeCode("TRIGGER_FAILURE"); // 假设此类型会触发失败
        
        // 执行开票请求
        RestResponse response = bilOrderController.makeInvoice(request);
        
        // 验证响应
        assertFalse("开票应该失败", response.getSuccess());
        
        // 验证订单状态已回滚到开票失败
        BilOrderEntity finalOrder = bilOrderService.getById(testOrder.getId());
        assertEquals("状态应回滚到开票失败", 
                InvoiceStateEnum.INVOICEFAILD.getValue(), 
                finalOrder.getInvoiceState());
        
        // 验证错误信息已记录到备注中
        assertNotNull("备注不应为空", finalOrder.getRemark());
        assertTrue("备注应包含错误信息", 
                finalOrder.getRemark().contains("开票失败"));
    }
} 