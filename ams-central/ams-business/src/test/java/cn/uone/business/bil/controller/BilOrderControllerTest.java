package cn.uone.business.bil.controller;

import cn.uone.application.enumerate.order.InvoiceStateEnum;
import cn.uone.bean.base.response.RestResponse;
import cn.uone.bean.entity.business.bil.BilOrderEntity;
import cn.uone.bean.entity.business.invoice.vo.InvoiceBuyerVo;
import cn.uone.business.bil.service.IBilOrderService;
import cn.uone.business.rpt.service.IReportInvoiceService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * BilOrderController发票开具功能测试类
 * 
 * 测试场景包括：
 * 1. 正常开票流程 - 验证订单状态正确更新，结果正确
 * 2. 重复开票防护 - 验证非"未开票"状态的订单无法再次开票
 * 3. 并发请求处理 - 模拟多线程并发请求，验证只有一个请求成功
 * 4. 状态回滚机制 - 开票失败时状态能正确回滚
 */
@RunWith(MockitoJUnitRunner.class)
public class BilOrderControllerTest {

    @InjectMocks
    private BilOrderController bilOrderController;

    @Mock
    private IBilOrderService bilOrderService;

    @Mock
    private IReportInvoiceService reportInvoiceService;

    private static final String TEST_ORDER_ID = "test-order-123";
    
    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 场景1：正常开票流程测试
     * 验证订单状态正确更新，开票请求成功处理
     */
    @Test
    public void testNormalInvoiceProcess() {
        // 模拟数据准备
        BilOrderEntity mockOrder = new BilOrderEntity();
        mockOrder.setId(TEST_ORDER_ID);
        mockOrder.setCode("ORD20250701001");
        mockOrder.setInvoiceState(InvoiceStateEnum.UNBILLED.getValue());

        // 模拟bilOrderService行为
        when(bilOrderService.getById(TEST_ORDER_ID)).thenReturn(mockOrder);
        when(bilOrderService.updateById(any(BilOrderEntity.class))).thenReturn(true);

        // 创建测试请求
        InvoiceBuyerVo invoiceRequest = new InvoiceBuyerVo();
        invoiceRequest.setOrderId(TEST_ORDER_ID);
        invoiceRequest.setInvoiceTypeCode("REPORT"); // 使用报表类型开票

        // 执行测试
        RestResponse response = bilOrderController.makeInvoice(invoiceRequest);

        // 验证结果
        assertTrue("开票应该成功", response.getSuccess());
        
        // 验证服务调用
        verify(bilOrderService, times(1)).getById(TEST_ORDER_ID);
        verify(bilOrderService, times(1)).updateById(any(BilOrderEntity.class));
        verify(reportInvoiceService, times(1)).makeInvoice(mockOrder);
    }

    /**
     * 场景2：重复开票防护测试
     * 验证非"未开票"状态的订单无法再次开票
     */
    @Test
    public void testPreventDuplicateInvoice() {
        // 准备测试数据 - 已开票状态的订单
        BilOrderEntity mockOrder = new BilOrderEntity();
        mockOrder.setId(TEST_ORDER_ID);
        mockOrder.setCode("ORD20250701002");
        mockOrder.setInvoiceState(InvoiceStateEnum.INVOICED.getValue()); // 已开票状态

        // 模拟bilOrderService行为
        when(bilOrderService.getById(TEST_ORDER_ID)).thenReturn(mockOrder);

        // 创建测试请求
        InvoiceBuyerVo invoiceRequest = new InvoiceBuyerVo();
        invoiceRequest.setOrderId(TEST_ORDER_ID);
        invoiceRequest.setInvoiceTypeCode("REPORT");

        // 执行测试
        RestResponse response = bilOrderController.makeInvoice(invoiceRequest);

        // 验证结果
        assertFalse("对已开票订单的请求应被拒绝", response.getSuccess());
        assertTrue("错误消息应包含状态提示", 
                response.getMessage().contains("当前状态为") && 
                response.getMessage().contains("不允许重复开票"));
        
        // 验证服务调用
        verify(bilOrderService, times(1)).getById(TEST_ORDER_ID);
        verify(bilOrderService, never()).updateById(any(BilOrderEntity.class)); // 不应更新订单状态
        verify(reportInvoiceService, never()).makeInvoice(any(BilOrderEntity.class)); // 不应调用开票服务
    }

    /**
     * 场景3：状态更新失败测试
     * 验证更新状态失败时请求被拒绝
     */
    @Test
    public void testStatusUpdateFailure() {
        // 准备测试数据
        BilOrderEntity mockOrder = new BilOrderEntity();
        mockOrder.setId(TEST_ORDER_ID);
        mockOrder.setCode("ORD20250701003");
        mockOrder.setInvoiceState(InvoiceStateEnum.UNBILLED.getValue());

        // 模拟bilOrderService行为 - 更新状态失败
        when(bilOrderService.getById(TEST_ORDER_ID)).thenReturn(mockOrder);
        when(bilOrderService.updateById(any(BilOrderEntity.class))).thenReturn(false);

        // 创建测试请求
        InvoiceBuyerVo invoiceRequest = new InvoiceBuyerVo();
        invoiceRequest.setOrderId(TEST_ORDER_ID);
        invoiceRequest.setInvoiceTypeCode("REPORT");

        // 执行测试
        RestResponse response = bilOrderController.makeInvoice(invoiceRequest);

        // 验证结果
        assertFalse("状态更新失败时请求应被拒绝", response.getSuccess());
        assertTrue("错误消息应包含更新失败提示", 
                response.getMessage().contains("更新订单状态失败"));
        
        // 验证服务调用
        verify(bilOrderService, times(1)).getById(TEST_ORDER_ID);
        verify(bilOrderService, times(1)).updateById(any(BilOrderEntity.class));
        verify(reportInvoiceService, never()).makeInvoice(any(BilOrderEntity.class)); // 不应调用开票服务
    }

    /**
     * 场景4：开票服务异常测试
     * 验证开票服务抛出异常时能正确回滚状态
     */
    @Test
    public void testInvoiceServiceException() {
        // 准备测试数据
        BilOrderEntity mockOrder = new BilOrderEntity();
        mockOrder.setId(TEST_ORDER_ID);
        mockOrder.setCode("ORD20250701004");
        mockOrder.setInvoiceState(InvoiceStateEnum.UNBILLED.getValue());

        // 模拟bilOrderService行为
        when(bilOrderService.getById(TEST_ORDER_ID)).thenReturn(mockOrder);
        when(bilOrderService.updateById(any(BilOrderEntity.class))).thenReturn(true);
        
        // 模拟reportInvoiceService抛出异常
        doThrow(new RuntimeException("开票服务异常")).when(reportInvoiceService).makeInvoice(any(BilOrderEntity.class));

        // 创建测试请求
        InvoiceBuyerVo invoiceRequest = new InvoiceBuyerVo();
        invoiceRequest.setOrderId(TEST_ORDER_ID);
        invoiceRequest.setInvoiceTypeCode("REPORT");

        // 执行测试
        RestResponse response = bilOrderController.makeInvoice(invoiceRequest);

        // 验证结果
        assertFalse("开票服务异常时请求应失败", response.getSuccess());
        assertTrue("错误消息应包含开票失败提示", 
                response.getMessage().contains("Report开票失败"));
        
        // 验证服务调用和状态回滚
        verify(bilOrderService, times(1)).getById(TEST_ORDER_ID);
        verify(bilOrderService, times(1)).updateById(any(BilOrderEntity.class)); // 初始状态更新
        verify(reportInvoiceService, times(1)).makeInvoice(any(BilOrderEntity.class));
        verify(bilOrderService, times(2)).getById(any(String.class)); // rollbackInvoiceStatus中再次获取订单
        verify(bilOrderService, times(2)).updateById(any(BilOrderEntity.class)); // 状态回滚更新
    }

    /**
     * 场景5：不支持的开票类型测试
     * 验证不支持的开票类型被正确拒绝
     */
    @Test
    public void testUnsupportedInvoiceType() {
        // 准备测试数据
        BilOrderEntity mockOrder = new BilOrderEntity();
        mockOrder.setId(TEST_ORDER_ID);
        mockOrder.setCode("ORD20250701005");
        mockOrder.setInvoiceState(InvoiceStateEnum.UNBILLED.getValue());

        // 模拟bilOrderService行为
        when(bilOrderService.getById(TEST_ORDER_ID)).thenReturn(mockOrder);
        when(bilOrderService.updateById(any(BilOrderEntity.class))).thenReturn(true);

        // 创建测试请求 - 使用不支持的开票类型
        InvoiceBuyerVo invoiceRequest = new InvoiceBuyerVo();
        invoiceRequest.setOrderId(TEST_ORDER_ID);
        invoiceRequest.setInvoiceTypeCode("UNSUPPORTED_TYPE");

        // 执行测试
        RestResponse response = bilOrderController.makeInvoice(invoiceRequest);

        // 验证结果
        assertFalse("不支持的开票类型应被拒绝", response.getSuccess());
        assertTrue("错误消息应包含类型不支持提示", 
                response.getMessage().contains("不支持的开票类型"));
        
        // 验证服务调用和状态回滚
        verify(bilOrderService, times(1)).getById(TEST_ORDER_ID);
        verify(bilOrderService, times(1)).updateById(any(BilOrderEntity.class)); // 初始状态更新
        verify(reportInvoiceService, never()).makeInvoice(any(BilOrderEntity.class)); // 不应调用开票服务
        verify(bilOrderService, times(2)).getById(any(String.class)); // rollbackInvoiceStatus中再次获取订单
        verify(bilOrderService, times(2)).updateById(any(BilOrderEntity.class)); // 状态回滚更新
    }

    /**
     * 场景6：百望开票流程测试
     * 验证百望开票类型请求被正确处理
     */
    @Test
    public void testBaiwangInvoiceProcess() {
        // 准备测试数据
        BilOrderEntity mockOrder = new BilOrderEntity();
        mockOrder.setId(TEST_ORDER_ID);
        mockOrder.setCode("ORD20250701006");
        mockOrder.setInvoiceState(InvoiceStateEnum.UNBILLED.getValue());

        // 模拟百望开票成功响应
        RestResponse mockBaiwangResponse = new RestResponse();
        mockBaiwangResponse.setSuccess(true);
        mockBaiwangResponse.setMessage("百望开票成功");

        // 模拟bilOrderService行为
        when(bilOrderService.getById(TEST_ORDER_ID)).thenReturn(mockOrder);
        when(bilOrderService.updateById(any(BilOrderEntity.class))).thenReturn(true);
        when(bilOrderService.baiwangInvoice(any(InvoiceBuyerVo.class))).thenReturn(mockBaiwangResponse);

        // 创建测试请求
        InvoiceBuyerVo invoiceRequest = new InvoiceBuyerVo();
        invoiceRequest.setOrderId(TEST_ORDER_ID);
        invoiceRequest.setInvoiceTypeCode("BAIWANG");

        // 执行测试
        RestResponse response = bilOrderController.makeInvoice(invoiceRequest);

        // 验证结果
        assertTrue("百望开票应成功", response.getSuccess());
        assertEquals("应返回百望服务的响应", mockBaiwangResponse, response);
        
        // 验证服务调用
        verify(bilOrderService, times(1)).getById(TEST_ORDER_ID);
        verify(bilOrderService, times(1)).updateById(any(BilOrderEntity.class));
        verify(bilOrderService, times(1)).baiwangInvoice(any(InvoiceBuyerVo.class));
    }

    /**
     * 场景7：并发请求测试
     * 模拟多线程并发请求，验证分布式锁机制生效（基于CacheLock注解）
     * 
     * 注意：由于@CacheLock是依赖于实际Redis环境的，单元测试中无法完全模拟其行为
     * 此测试仅验证控制器处理逻辑，真实并发控制需在集成环境中测试
     */
    @Test
    public void testConcurrentRequests() throws Exception {
        // 测试参数
        final int THREAD_COUNT = 10; // 并发线程数
        final CountDownLatch startLatch = new CountDownLatch(1);
        final CountDownLatch endLatch = new CountDownLatch(THREAD_COUNT);
        final AtomicInteger successCount = new AtomicInteger(0);
        final List<Exception> exceptions = new ArrayList<>();
        
        // 准备测试数据
        BilOrderEntity mockOrder = new BilOrderEntity();
        mockOrder.setId(TEST_ORDER_ID);
        mockOrder.setCode("ORD20250701007");
        mockOrder.setInvoiceState(InvoiceStateEnum.UNBILLED.getValue());

        // 模拟第一次获取订单状态为未开票，后续为开票中
        when(bilOrderService.getById(TEST_ORDER_ID)).thenAnswer(invocation -> {
            if (successCount.get() == 0) {
                BilOrderEntity order = new BilOrderEntity();
                order.setId(TEST_ORDER_ID);
                order.setCode("ORD20250701007");
                order.setInvoiceState(InvoiceStateEnum.UNBILLED.getValue());
                return order;
            } else {
                BilOrderEntity order = new BilOrderEntity();
                order.setId(TEST_ORDER_ID);
                order.setCode("ORD20250701007");
                order.setInvoiceState(InvoiceStateEnum.INVOICING.getValue());
                return order;
            }
        });
        
        when(bilOrderService.updateById(any(BilOrderEntity.class))).thenReturn(true);
        
        // 创建并发测试线程池
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_COUNT);
        
        for (int i = 0; i < THREAD_COUNT; i++) {
            executor.submit(() -> {
                try {
                    // 等待所有线程准备好同时开始
                    startLatch.await();
                    
                    // 创建开票请求
                    InvoiceBuyerVo request = new InvoiceBuyerVo();
                    request.setOrderId(TEST_ORDER_ID);
                    request.setInvoiceTypeCode("REPORT");
                    
                    // 执行开票请求
                    RestResponse response = bilOrderController.makeInvoice(request);
                    
                    // 统计成功请求
                    if (response.getSuccess()) {
                        successCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    exceptions.add(e);
                } finally {
                    endLatch.countDown();
                }
            });
        }
        
        // 同时触发所有线程开始执行
        startLatch.countDown();
        
        // 等待所有线程执行完成
        endLatch.await();
        executor.shutdown();
        
        // 验证结果
        assertEquals("并发请求中应只有一个成功", 1, successCount.get());
        assertEquals("不应有异常发生", 0, exceptions.size());
    }

    /**
     * 场景8：订单不存在测试
     * 验证订单不存在时请求被拒绝
     */
    @Test
    public void testOrderNotFound() {
        // 模拟订单不存在
        when(bilOrderService.getById(TEST_ORDER_ID)).thenReturn(null);

        // 创建测试请求
        InvoiceBuyerVo invoiceRequest = new InvoiceBuyerVo();
        invoiceRequest.setOrderId(TEST_ORDER_ID);
        invoiceRequest.setInvoiceTypeCode("REPORT");

        // 执行测试
        RestResponse response = bilOrderController.makeInvoice(invoiceRequest);

        // 验证结果
        assertFalse("订单不存在时请求应被拒绝", response.getSuccess());
        assertEquals("应返回订单不存在提示", "订单不存在", response.getMessage());
        
        // 验证服务调用
        verify(bilOrderService, times(1)).getById(TEST_ORDER_ID);
        verify(bilOrderService, never()).updateById(any(BilOrderEntity.class));
        verify(reportInvoiceService, never()).makeInvoice(any(BilOrderEntity.class));
    }
} 