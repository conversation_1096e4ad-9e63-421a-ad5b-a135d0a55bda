# 发票开具并发测试计划

## 测试目的

验证BilOrderController.makeInvoice方法在高并发情况下的正确性和性能，特别是以下几个方面：

1. 防重复开票机制：确保同一订单只能成功开具一次发票
2. 分布式锁性能：测试@CacheLock注解的并发控制效果
3. 状态原子更新：验证订单状态更新的原子性
4. 异常处理：测试在并发场景下异常处理的健壮性
5. 系统整体性能：测量在高并发情况下系统的响应时间和吞吐量

## 测试环境要求

- JDK 8+
- Apache JMeter 5.0+
- MySQL 5.7+（用于准备和验证测试数据）
- Redis（用于分布式锁）
- 应用服务器（测试目标）

## 测试计划文件说明

本目录包含以下测试计划文件：

- `invoice-concurrency-test.jmx`：JMeter测试计划，模拟50个并发用户同时请求开票
- `invoice-load-test.jmx`：JMeter负载测试计划，持续增加用户负载直到系统瓶颈出现

## 执行测试步骤

1. 启动测试环境的应用服务器
2. 使用SQL脚本准备测试数据（`/sql/prepare-test-order.sql`）
3. 打开JMeter并加载测试计划文件
4. 根据实际环境修改以下参数：
   - 服务器域名和端口
   - 用户并发数
   - 测试持续时间
5. 运行测试
6. 分析结果，主要关注：
   - 成功率（应有且只有一个请求成功）
   - 响应时间
   - 错误信息（应为"不允许重复开票"或类似消息）
   - 服务器资源使用情况

## 结果验证

测试成功的标准：

1. 在并发请求中，只有一个请求成功开票
2. 其他请求都返回合理的错误信息，如"订单已开票"、"订单状态为已开票，不允许重复开票"等
3. 数据库中的订单状态正确更新为已开票
4. 系统在高并发下保持稳定，无异常崩溃
5. 响应时间在合理范围内（根据系统性能要求定义）

## 注意事项

- 每次测试前请确保数据库中的测试订单已重置为未开票状态
- 测试环境应与生产环境尽可能接近
- 在高并发测试中，应监控服务器资源使用情况，及时发现瓶颈
- 测试完成后应清理测试数据 