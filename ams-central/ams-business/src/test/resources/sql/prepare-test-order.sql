-- 清除可能存在的测试数据
DELETE FROM t_bil_order WHERE id = 'TEST_ORDER_ID';

-- 插入测试订单数据
INSERT INTO t_bil_order (
    id, 
    code, 
    invoice_state, 
    payment, 
    payable_payment, 
    order_type, 
    pay_state,
    create_date,
    update_date
) VALUES (
    'TEST_ORDER_ID',
    'TEST-ORDER-2025-001',
    '0', -- UNBILLED 未开票状态
    100.00, -- 账单金额
    100.00, -- 应付金额
    '1',  -- 订单类型
    '10', -- 支付状态
    NOW(),
    NOW()
);

-- 插入测试订单明细数据
INSERT INTO t_bil_order_item (
    id,
    order_id,
    order_item_type,
    payment,
    create_date,
    update_date
) VALUES (
    'TEST_ORDER_ITEM_ID',
    'TEST_ORDER_ID',
    '20', -- 租金
    100.00,
    NOW(),
    NOW()
); 