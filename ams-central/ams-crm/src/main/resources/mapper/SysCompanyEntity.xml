<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.crm.dao.SysCompanyDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.crm.SysCompanyEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="is_account" property="isAccount" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="bank_code" property="bankCode" />
        <result column="bank" property="bank" />
        <result column="card" property="card" />
        <result column="split_proportion" property="splitProportion" />
        <result column="merchant_id" property="merchantId" />
        <result column="contact" property="contact" />
        <result column="telphone" property="telphone" />
        <result column="legal_person" property="legalPerson" />
        <result column="type" property="type" />
        <result column="parent_id" property="parentId" />
        <result column="parent_name" property="parentName" />
    </resultMap>

    <resultMap type="cn.uone.application.vo.tree.TreeNode" id="ProjectMap">
        <result column="id" property="id"/>
        <result column="title" property="title" />
        <result column="type" property="type" />
        <result property="pid" column="pid"/>
        <collection property="children" ofType="cn.uone.application.vo.tree.TreeNode">
            <result property="id" column="proj.id"/>
            <result property="title" column="proj.title"/>
            <result property="type" column="proj.type"/>
            <result property="pid" column="proj.pid"/>
            <collection property="children" ofType="cn.uone.application.vo.tree.TreeNode">
                <result property="id" column="part.id"/>
                <result property="title" column="part.title"/>
                <result property="type" column="part.type"/>
                <result property="pid" column="part.pid"/>
            </collection>
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        is_account, name, code, bank_code, bank, card, split_proportion, merchant_id, contact, telphone, legal_person, type, parent_id, parent_name
    </sql>

    <select id="getCompanyList" resultMap="BaseResultMap">
        select t.*
        from sys_company t
        where 1=1
        <if test="map.company !=null">
            <if test="map.company.id !=null and map.company.id !=''">
                and t.id = #{map.company.id}
            </if>
            <if test="map.company.name !=null and map.company.name !=''">
                and t.name like "%"#{map.company.name}"%"
            </if>
            <if test="map.company.contact !=null and map.company.contact !=''">
                and t.contact like "%"#{map.company.contact}"%"
            </if>
            <if test="map.company.telphone !=null and map.company.telphone !=''">
                and t.telphone like "%"#{map.user.telphone}"%"
            </if>
            <if test="map.company.parentName !=null and map.company.parentName !=''">
                and t.parent_name like "%"#{map.company.parentName}"%"
            </if>
        </if>
        <if test="map.self !=null and map.self !=''">
            AND EXISTS(SELECT 1 FROM sys_user u
            WHERE u.`company_id`=t.id AND u.id=#{map.self})
        </if>
    </select>

    <select id="getProjectsByScopes" resultMap="ProjectMap">
        SELECT t.id,t.name title,t.code,t.parent_id pid,
        IF(t.parent_id='1' OR t.parent_id='0','',CONCAT(t.parent_name,'->')) parent_name,
        'comp' AS `type`,
        p.id AS "proj.id",
        p.name AS "proj.title",
        'proj' AS "proj.type",
        p.company_id AS "proj.pid",
        pp.id AS "part.id",
        pp.name AS "part.title",
        'part' AS "part.type",
        pp.project_id AS "part.pid"
        FROM sys_company t
        LEFT JOIN v_res_project p ON p.company_id=t.id
        LEFT JOIN v_res_plan_partition pp ON pp.project_id=p.id
        where 1=1 and p.operate_state='0'
        <if test="map.companyCode !=null and map.companyCode !=''">
            and t.code like #{map.companyCode}"%"
        </if>
        ORDER BY t.name,p.name,pp.name
    </select>

</mapper>
