<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.crm.dao.ModularDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.crm.ModularEntity">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="name" property="name"/>
        <result column="icon_url" property="iconUrl" />
    </resultMap>

    <resultMap id="ModularMap" type="cn.uone.bean.entity.crm.ModularEntity">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="route" property="route"/>
        <result column="icon" property="icon"/>
        <result column="icon_url" property="iconUrl" />
        <collection property="menus" javaType="ArrayList" ofType="cn.uone.bean.entity.crm.MenuEntity">
            <id property="id" column="menu.id"/>
            <result property="name" column="menu.name"/>
            <result property="href" column="menu.href"/>
            <result property="route" column="menu.route"/>
            <result property="icon" column="menu.icon"/>
            <result property="isShow" column="menu.is_show"/>
        </collection>
    </resultMap>


    <resultMap id="SystemCodeMap" type="cn.uone.bean.entity.crm.UoneSystemEntity">
        <result column="code" property="code"/>
        <result column="name" property="name"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        name
    </sql>

    <select id="selectMenus" resultMap="ModularMap" parameterType="java.util.Map">
        select modu.id,modu.name ,modu.icon,modu.route,modu.icon_url,
        sm.name as "menu.name",
        sm.href as "menu.href",
        sm.is_show as "menu.is_show",
        sm.icon as "menu.icon",
        sm.route as "menu.route",
        sm.id as "menu.id"
        from sys_menu sm,sys_modular modu,(
        select so.menu_id
        from sys_user_role suo,sys_role_operator sro,sys_operator so
        where suo.role_id = sro.role_id and sro.operator_id = so.id
        <if test="userId !=null and userId !=''">
            and suo.user_id = #{userId}
        </if>
        group by so.menu_id
        ) t where sm.modular_id = modu.id and t.menu_id = sm.id
        <if test="systemCode !=null and systemCode !=''">
          and modu.system_code = #{systemCode}
        </if>
        and sm.is_show = '1'
        order by modu.sort asc ,sm.sort asc
    </select>

    <select id="getSystemCodes" resultMap="SystemCodeMap">
        select s.code,s.name from sys_system s
    </select>

</mapper>
