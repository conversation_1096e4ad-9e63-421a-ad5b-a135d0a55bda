<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.crm.dao.MenuDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.crm.MenuEntity">
    <result column="id" property="id" />
    <result column="create_by" property="createBy" />
    <result column="create_date" property="createDate" />
    <result column="update_by" property="updateBy" />
    <result column="update_date" property="updateDate" />
        <result column="name" property="name" />
        <result column="parent_id" property="parentId" />
        <result column="level" property="level" />
        <result column="parent_ids" property="parentIds" />
        <result column="sort" property="sort" />
        <result column="href" property="href" />
        <result column="target" property="target" />
        <result column="icon" property="icon" />
        <result column="bg_color" property="bgColor" />
        <result column="is_show" property="isShow"/>
        <result column="permission" property="permission" />
        <result column="remarks" property="remarks" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        name, parent_id, level, parent_ids, sort, href, target, icon, bg_color, is_show, permission, remarks
    </sql>


</mapper>
