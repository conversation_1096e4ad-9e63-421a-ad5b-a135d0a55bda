<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.uone.crm.dao.DataScopeDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.uone.bean.entity.crm.DataScopeEntity">
        <result column="id" property="id"/>
        <result column="create_date" property="createDate"/>
        <result column="create_by" property="createBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="user_id" property="userId"/>
        <result column="scope_id" property="scopeId"/>
        <result column="scope_type" property="scopeType"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="ScopeCityMap" type="cn.uone.bean.entity.crm.CityEntity">
        <result column="city_code" property="cityCode"/>
        <result column="city" property="city"/>
        <collection property="projects" ofType="cn.uone.bean.entity.crm.ProjectEntity">
            <result column="city_code" property="cityCode"/>
            <result column="city" property="city"/>
            <result column="p.id" property="id"/>
            <result column="p.name" property="name"/>
            <result column="p.area" property="area"/>
            <result column="p.room_num" property="roomNum"/>
            <result column="p.address" property="address"/>
            <result column="p.longitude" property="longitude"/>
            <result column="p.latitude" property="latitude"/>
            <result column="p.expand_state" property="expandState"/>
            <result column="p.operate_state" property="operateState"/>
        </collection>
    </resultMap>

    <resultMap id="ScopeProjectMap" type="cn.uone.bean.entity.crm.ProjectEntity">
        <result column="city_code" property="cityCode"/>
        <result column="city" property="city"/>
        <result column="p.id" property="id"/>
        <result column="p.name" property="name"/>
        <result column="p.area" property="area"/>
        <result column="p.room_num" property="roomNum"/>
        <result column="p.address" property="address"/>
        <result column="p.longitude" property="longitude"/>
        <result column="p.latitude" property="latitude"/>
        <result column="p.expand_state" property="expandState"/>
        <result column="p.operate_state" property="operateState"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_date,
        create_by,
        update_date,
        update_by,
        user_id, scope_id, scope_type
    </sql>

    <select id="getScopeCitys" resultMap="ScopeCityMap">
        select
          p.city,p.city_code,
          p.id as "p.id",p.name as "p.name",
          p.area as "p.area",p.room_num as "p.room_num",
          p.address as "p.address",p.longitude as "p.longitude",
          p.latitude as "p.latitude",p.expand_state as "p.expand_state",
          p.operate_state as "p.operate_state"
         from v_res_project p
         where 1=1 #project_datascope#
    </select>

    <select id="getScopeProjects" resultMap="ScopeProjectMap">
        select
          p.city,p.city_code,
          p.id as "p.id",p.name as "p.name",
          p.area as "p.area",p.room_num as "p.room_num",
          p.address as "p.address",p.longitude as "p.longitude",
          p.latitude as "p.latitude",p.expand_state as "p.expand_state",
          p.operate_state as "p.operate_state"
         from v_res_project p
         where 1=1 #project_datascope#
    </select>

    <select id="getScopeProjectsByAllCity" resultMap="ScopeCityMap">

         select c.name city,c.code city_code,
                  p.id as "p.id",p.name as "p.name",
                  p.area as "p.area",p.room_num as "p.room_num",
                  p.address as "p.address",p.longitude as "p.longitude",
                  p.latitude as "p.latitude",p.expand_state as "p.expand_state",
                  p.operate_state as "p.operate_state"
             from v_res_city c
             left join (select * from v_res_project t where 1=1 #project_datascope#) p on p.city_code = c.code

    </select>

</mapper>
