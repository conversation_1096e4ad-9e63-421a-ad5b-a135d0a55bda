package cn.uone.crm.controller.zzct;

import cn.hutool.core.lang.Console;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.uone.application.constant.BaseConstants;
import cn.uone.application.enumerate.SystemCodeEnum;
import cn.uone.bean.entity.crm.*;
import cn.uone.bean.parameter.SysLogPo;
import cn.uone.cache.util.CacheUtil;
import cn.uone.crm.service.*;
import cn.uone.crm.util.AESEncryptor;
import cn.uone.fegin.crm.ISecuityFegin;
import cn.uone.fegin.tpi.IQyWechatFegin;
import cn.uone.shiro.bean.UonePermissions;
import cn.uone.shiro.util.JWTUtil;
import cn.uone.shiro.util.ShiroUser;
import cn.uone.shiro.util.UoneSysUser;
import cn.uone.util.AlgorUtil;
import cn.uone.util.CodeUtil;
import cn.uone.web.base.BusinessException;
import cn.uone.web.base.RestResponse;
import cn.uone.web.base.SysLog;
import cn.uone.web.util.UoneHeaderUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.wf.captcha.Captcha;
import com.wf.captcha.GifCaptcha;
import com.wf.captcha.SpecCaptcha;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 从漳州城投迁移过来，漳州城投的后台登录方法采用密码错误5次自动锁定策略
 * 后台登录方法改成用户名、密码、短信验证码的验证方式
 * caizhanghe edit 20250105
 */
@Api(value="授权登录服务",tags={"用户登录、操作日志等接口"})
@RestController
@RequestMapping("/zzct/uaa")
@Slf4j
public class ZzctSecuityController implements ISecuityFegin {

    @Autowired
    private IOperationLogService operationLogService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IRoleService roleService;

    @Autowired
    private IModularService modularService;
    @Autowired
    private IQyWechatFegin qyWechatFegin;

    @Autowired
    private ILoginLogService loginLogService ;

    @Autowired
    private IZzctUserService zzctUserService;

    @Value("${domeke.projectUrl}")
    private String projectUrl;

    @ApiOperation(value="保存操作日志", notes="根据SysLog对象创建用户")
    @RequestMapping(value = "/saveOperationLog", method = RequestMethod.POST)
    public void saveOperationLog(@RequestBody SysLog log){
        OperationLogEntity entity = new OperationLogEntity();
        entity.setIp(log.getIp());
        entity.setMethod(log.getMethod());
        entity.setOperation(log.getOperation());
        entity.setParams(log.getParams());
        entity.setCreateBy(log.getCreater());
        entity.setResult(log.getResult());
        entity.setMsg(log.getMsg());
        operationLogService.save(entity);
    }

    @GetMapping("/log/page")
    public RestResponse logPage(Page<OperationLogEntity> page, SysLogPo log){
        return RestResponse.success().setData(operationLogService.page(page,log));
    }

    @GetMapping("/log/loginPage")
    public RestResponse loginPage(Page<LoginLogEntity> page, SysLogPo log){
        return RestResponse.success().setData(loginLogService.page(page,log));
    }


    @ApiOperation(value = "获取系统菜单", notes = "根据权限的获取")
    @GetMapping("/getMenus")
    public RestResponse getMenus(@RequestParam("systemCode") String systemCode) {
        List<ModularEntity> list = modularService.getMenu(systemCode);
        return RestResponse.success().setData(list);
    }

    @Override
    @ApiOperation(value = "保存接口日志", notes = "保存接口日志")
    @PostMapping("/saveApiLog")
    public RestResponse saveApiLog(@RequestBody ApiLogEntity apiLog) {
        if(ObjectUtil.isNotNull(apiLog)){
            apiLog.insert();
        }
        return RestResponse.success();
    }

    @RequestMapping(value = "/isAuthByPermissions", method = RequestMethod.GET)
    @ApiOperation("根据权限标识查询，当前用户是否拥有该权限")
    public RestResponse isAuthByPermissions(@RequestParam("permissions") String permissions) {

        if (ObjectUtil.isNull(UoneSysUser.ShiroUser()) || ObjectUtil.isNull(UoneSysUser.ShiroUser().getPermissions())) {
            return RestResponse.failure("当前用户不拥有" + permissions + "权限");
        }
        if (UoneSysUser.ShiroUser().getPermissions().contains(permissions)) {
            return RestResponse.success();
        } else {
            return RestResponse.failure("当前用户不拥有" + permissions + "权限");
        }
    }

    @ApiOperation("刷新当前用户缓存信息")
    @RequestMapping(value = "/refresh", method = RequestMethod.POST)
    public RestResponse refresh() {
        UserEntity user = userService.findUserByCurrent();
        ShiroUser shiroUser = userService.getShiroUser(user);
        JWTUtil.putUserInfo(shiroUser);
        return RestResponse.success().setData(shiroUser);
    }

    @RequestMapping(value = "/login", method = RequestMethod.POST)
    @UonePermissions()
    public RestResponse login(@RequestParam("username") String usernameEnc,
                              @RequestParam("password") String passwordEnc,String type,@RequestParam("smsCode") String smsCodeEnc) {
        String username = null;
        String password = null;
        String smsCode = null;
        try {
            username = AESEncryptor.decrypt(usernameEnc);
            password = AESEncryptor.decrypt(passwordEnc);
            smsCode = AESEncryptor.decrypt(smsCodeEnc);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if(StrUtil.isBlank(type)){
            return RestResponse.failure("请确认登录系统").code(406);
        }

        SystemCodeEnum en = SystemCodeEnum.getEnumByValue(type);
        if(null==en){
            return RestResponse.failure("请确认登录系统").code(406);
        }

        /*if (StrUtil.isBlank(code)) {
            return RestResponse.failure("请输入图片验证码").code(406);
        }*/

        //验证图片验证码
        /*if (!CacheUtil.validCaptcha(codeKey, code)) {
            return RestResponse.failure("请输入正确的图片验证码").code(406);
        }*/

        UserEntity user = userService.findUserByLoginName(username);
        if (user == null) {
            return RestResponse.failure("用户名或密码错误！").code(406);
        }
        //判读是否锁住
        if(user.getLocked()){
            return RestResponse.failure("该用户已被禁用！").code(406);
        }
        String tel = user.getTel();
        if (StrUtil.isBlank(smsCode)) {
            return RestResponse.failure("请输入正确的短信验证码").code(406);
        }
        String vildCode = (String) CacheUtil.get(BaseConstants.TEL_SMS_HEADER + tel);
        if (!smsCode.equals(vildCode)) {
            return RestResponse.failure("短信验证码错误或已过期（5分钟内）！").code(406);
        }
        //密码加密验证
        password = AlgorUtil.entryptPassword(password, user.getSalt());
        if (!user.getPassword().equals(password)) {
            int loginErrCount = user.getLoginErrCount();//增加密码错误次数校验
            loginErrCount = loginErrCount + 1;//账号密码输入错误，错误次数加1
            if(loginErrCount>=5){//错误次数超过5次,对账号进行锁定
                user.setLocked(true);
                user.setLoginErrCount(loginErrCount);
                userService.updateUser(user);
                return RestResponse.failure("密码错误次数已达5次,该账号已被锁定,请联系管理员！").code(406);
            } else if(loginErrCount==4){
                user.setLoginErrCount(loginErrCount);
                userService.updateUser(user);
                return RestResponse.failure("密码错误次数已达4次,达到5次时将被锁定！").code(406);
            }  else if(loginErrCount==3){
                user.setLoginErrCount(loginErrCount);
                userService.updateUser(user);
                return RestResponse.failure("密码错误次数已达3次,达到5次时将被锁定！").code(406);
            } else{
                user.setLoginErrCount(loginErrCount);//账号密码输入错误，错误次数加1
                userService.updateUser(user);
            }
            return RestResponse.failure("用户名或密码错误").code(406);
        } else {
            String system = user.getSystem();
            if(StrUtil.isBlank(system)){
                return RestResponse.failure("该用户不可登录"+en.getName()+"！").code(406);
            }

            if(!ArrayUtil.contains( system.split(","),en.getValue())){
                return RestResponse.failure("该用户不可登录"+en.getName()+"！").code(406);
            }
            String token = JWTUtil.createToken(username, userService.getShiroUser(user));
            Map<String, String> map = Maps.newHashMap();
            map.put(BaseConstants.HTTP_HEADER_NAME, token);
            //map.put("domeke_token", token);
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            toSaveLoginLog(en.getName(),username,smsCode,getIpAddress(request),"PC端登陆");
            user.setLoginErrCount(0);//如果登录成功,将累计登录错误次数清空
            userService.updateUser(user);
            return RestResponse.success().setData(map).code(200);
        }
    }

    private void toSaveLoginLog(String type ,String userName ,String code,String ips,String leiXing){
        LoginLogEntity loginLogEntity = new LoginLogEntity() ;
        loginLogEntity.setIps(ips);
        loginLogEntity.setConnection(userName);
        loginLogEntity.setHost(type);
        loginLogEntity.setAcceptlanguage(code);
        loginLogEntity.setOperatorids(leiXing);
        loginLogEntity.insertOrUpdate();
    }

    /***
     * 获取真实IP
     * @param request
     * @return
     */
    public static String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }


    @RequestMapping(value = "/app/login", method = RequestMethod.POST)
    @UonePermissions()
    public RestResponse appLogin(@RequestParam("username") String username,
                                 @RequestParam("password") String password, @RequestParam("code") String code, @RequestParam("codeKey") String codeKey) {

        if (StrUtil.isBlank(code)) {
            return RestResponse.failure("请输入图片验证码").code(406);
        }

        //验证图片验证码
        if (!CacheUtil.validCaptcha(codeKey, code)) {
            return RestResponse.failure("请输入正确的图片验证码").code(406);
        }

        UserEntity user = userService.findUserByLoginName(username);
        if (user == null) {
            return RestResponse.failure("用户名或密码错误！").code(406);
        }
        //密码加密验证
        password = AlgorUtil.entryptPassword(password, user.getSalt());
        if (!user.getPassword().equals(password)) {
            return RestResponse.failure("用户名或密码错误").code(406);
        } else {
            String token = JWTUtil.createAppToken(username, userService.getShiroUser(user));
            Map<String, String> map = Maps.newHashMap();
            map.put(BaseConstants.HTTP_HEADER_NAME, token);
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            toSaveLoginLog("XX管家",username,code,getIpAddress(request),"PC端登陆");
            return RestResponse.success().setData(map).code(200);
        }
    }

    @RequestMapping(value = "/approval/login", method = RequestMethod.POST)
    @UonePermissions()
    public RestResponse approvalLogin(@RequestParam("code") String code, @RequestParam(value = "isNotApp", required = false) Boolean isNotApp) {
        String qywechatid = qyWechatFegin.getuserinfo(code,"",isNotApp);
        System.out.println("企业微信登录id"+qywechatid);
        if (StrUtil.isBlank(qywechatid)) {
            return RestResponse.failure("企业微信授权失败！").code(406);
        }
        UserEntity user = userService.findUserByQywechat(qywechatid);
        if (user == null) {
            return RestResponse.failure("未同步企业微信用户！").code(406);
        }
        String token = "";

        if (null != isNotApp && isNotApp) {
            token = JWTUtil.createToken(user.getLoginName(), userService.getShiroUser(user));
        } else {
            token = JWTUtil.createAppToken(user.getLoginName(), userService.getShiroUser(user));
        }

        Map<String, Object> map = Maps.newHashMap();
        map.put(BaseConstants.HTTP_HEADER_NAME, token);
        map.put("user", user);
        return RestResponse.success().setData(map).code(200);
    }

    @GetMapping("/getScopeCitys")
    @ApiOperation("获取数据权限下城市")
    public RestResponse getScopeCitys() {

        return RestResponse.success().setData(roleService.getScopeCitys(true));
    }

    @GetMapping("/getScopeCitysByUt")
    @ApiOperation("获取数据权限下城市")
    public RestResponse getScopeCitysByUt() {

        return RestResponse.success().setData(roleService.getScopeCitys(false));
    }

    @GetMapping("/getScopeProjects")
    @ApiOperation("获取数据权限下项目")
    public RestResponse getScopeProjects() {
        return RestResponse.success().setData(roleService.getScopeProjects());
    }




    /**
     * 获取验证码（Gif版本）
     *
     * @param response
     */
    @RequestMapping(value = "/getCode", method = RequestMethod.GET)
    @UonePermissions
    public void getCode(HttpServletResponse response, HttpServletRequest request) throws BusinessException {
        try {
            response.setHeader("Pragma", "No-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setDateHeader("Expires", 0);
            response.setContentType("image/gif");
            /**
             * gif格式动画验证码
             * 宽，高，位数。
             */
            Captcha captcha = new SpecCaptcha(146, 38, 4);

            //存入Redis
            String codeKey = request.getParameter("codeKey");
            if (StrUtil.isNotEmpty(codeKey)) {
                CacheUtil.putEx(CacheUtil.CAPTCHA_NAMESPACE + codeKey, captcha.text().toLowerCase(), 300l);
            }
            //输出
            captcha.out(response.getOutputStream());

        } catch (Exception e) {
            throw new BusinessException("获取验证码异常：" + e.getMessage());
        }
    }

    /**
     * 获取验证码（Gif版本）
     *
     * @param response
     */
    @RequestMapping(value = "/getGifCode", method = RequestMethod.GET)
    @UonePermissions
    public void getGifCode(HttpServletResponse response, HttpServletRequest request) throws BusinessException {
        try {
            response.setHeader("Pragma", "No-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setDateHeader("Expires", 0);
            response.setContentType("image/gif");
            /**
             * gif格式动画验证码
             * 宽，高，位数。
             */
            Captcha captcha = new GifCaptcha(146, 38, 4);

            //存入Redis
            String codeKey = request.getParameter("codeKey");
            if (StrUtil.isNotEmpty(codeKey)) {
                CacheUtil.putEx(CacheUtil.CAPTCHA_NAMESPACE + codeKey, captcha.text().toLowerCase(), 300l);
            }
            //输出
            captcha.out(response.getOutputStream());

        } catch (Exception e) {
            throw new BusinessException("获取验证码异常：" + e.getMessage());
        }
    }

    /**
     * 获取验证码（Gif版本）
     *
     * @param codeKey
     */
    @RequestMapping(value = "/getCodeForTpi", method = RequestMethod.GET)
    @UonePermissions
    public RestResponse getCodeForTpi(@RequestParam("codeKey") String codeKey) throws BusinessException {
        String code = null;
        try {
            /**
             * gif格式动画验证码
             * 宽，高，位数。
             */
            Captcha captcha = new GifCaptcha(146, 38, 4);
            code = captcha.text().toLowerCase();
            //存入Redis
            if (StrUtil.isNotEmpty(codeKey)) {
                CacheUtil.putEx(CacheUtil.CAPTCHA_NAMESPACE + codeKey,code, 300l);
            }
        } catch (Exception e) {
            throw new BusinessException("获取验证码异常：" + e.getMessage());
        }
        return RestResponse.success().setData(code);
    }

    @RequestMapping(value = "/logout", method = RequestMethod.GET)
    public RestResponse logout() {
        String token = UoneHeaderUtil.getToken();
        String loginType = JWTUtil.getLoginType(token) + ":";
        CacheUtil.delete(JWTUtil.REDIS_NAMESPACE + loginType + token);
        return RestResponse.success();
    }

    @RequestMapping(value = "/getWeChatLoginUrl", method = RequestMethod.GET)
    @UonePermissions
    public RestResponse getWeChatLoginUrl(@RequestParam(required = false) String systemCode) {
        if(SystemCodeEnum.PROJECT.getValue().equals(systemCode)){
            return RestResponse.success().setData(qyWechatFegin.getProLoginUrl());
        }
        return RestResponse.success().setData(qyWechatFegin.getLoginUrl());
    }

    @RequestMapping(value = "/getWeChatPcLoginUrl", method = RequestMethod.GET)
    @UonePermissions
    public RestResponse getWeChatPcLoginUrl(@RequestParam(required = false) String systemCode) {
        if(SystemCodeEnum.PROJECT.getValue().equals(systemCode)){
            return RestResponse.success().setData(qyWechatFegin.getProPcLoginUrl());
        }else{
            return RestResponse.success().setData(qyWechatFegin.getPcLoginUrl());
        }
    }

    @RequestMapping(value = "/sendSmsCode", method = RequestMethod.POST)
    @UonePermissions
    public RestResponse sendSmsCode(String loginName,String code, String codeKey){

        if (StrUtil.isBlank(code)) {
            return RestResponse.failure("请输入图片验证码").code(406);
        }
        if (!CacheUtil.validCaptcha(codeKey, code)) {
            return RestResponse.failure("请输入正确的图片验证码").code(406);
        }

        String vild = CodeUtil.getRandom(4);

        UserEntity user =  userService.findUserByLoginName(loginName);
        if(ObjectUtil.isNull(user)){
            return RestResponse.failure("未查找到用户名为："+loginName+"的用户");
        }
        if(StrUtil.isBlank(user.getTel())){
            return RestResponse.failure(loginName+"用户未设置手机号");
        }
        String tel = user.getTel();
        Console.log("手机：" +tel + "验证码为：" + vild);
        //发送手机短信并保存验证码到缓存
        return zzctUserService.getRestResponse(vild, tel);
    }

    /**
     * 适用于漳州城投接收短信验证码
     * 主要是在后台登录时
     * @param username
     * @param code
     * @param codeKey
     * caizhanghe edit 2025-01-05
     * @return
     */
    @RequestMapping(value = "/sendSmsCodeForZzct", method = RequestMethod.POST)
    @UonePermissions()
    @ApiOperation("发送手机验证码")
    public RestResponse sendSmsCodeForZzct(@RequestParam("username") String username,@RequestParam("password")String password,String code,String codeKey) {
        try {
            username = AESEncryptor.decrypt(username);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        UserEntity user = userService.findUserByLoginName(username);
        if (user == null) {
            return RestResponse.failure("该用户不存在，请检查！").code(406);
        }
        //判读是否锁住
        if(user.getLocked()){
            return RestResponse.failure("该用户已被禁用！").code(406);
        }
        String tel = user.getTel();
        if (StrUtil.isBlank(tel)) {
            return RestResponse.failure("该用户未配置手机号，请检查！").code(406);
        }
        /*if (StrUtil.isBlank(code)) {
            return RestResponse.failure("请输入图片验证码").code(406);
        }
        if (!CacheUtil.validCaptcha(codeKey, code)) {
            return RestResponse.failure("请输入正确的图片验证码").code(406);
        }*/

        String vild = CodeUtil.getRandom(6);

        Console.log("手机：" + tel + "验证码为：" + vild);
        //发送手机短信并保存验证码到缓存
        return zzctUserService.getRestResponse(vild, tel);
    }

    @RequestMapping(value = "/resetPass", method = RequestMethod.POST)
    @UonePermissions
    public RestResponse resetPass(String loginName,String smsCode,String newPwd,String surePwd){
        if(!newPwd.equals(surePwd)){
            return  RestResponse.success("两次密码输入不一致");
        }
        UserEntity user =  userService.findUserByLoginName(loginName);
        if(ObjectUtil.isNull(user)){
            return RestResponse.failure("未查找到用户名为："+loginName+"的用户");
        }
        if(StrUtil.isBlank(user.getTel())){
            return RestResponse.failure(loginName+"用户未设置手机号");
        }
        if (StrUtil.isBlank(smsCode)) {
            return RestResponse.failure("请输入验证码").code(406);
        }
        String tel = user.getTel();

        String vildCode = (String) CacheUtil.get(BaseConstants.TEL_SMS_HEADER + tel);
        if (!smsCode.equals(vildCode)) {
            return RestResponse.failure("短信验证码错误或已过期（5分钟内）！").code(406);
        }

        user.setPassword(newPwd);
        AlgorUtil.entryptPassword(user);
        user.updateById();

        return RestResponse.success("修改密码成功");
    }

    @RequestMapping(value = "/getProjectSystemUrl", method = RequestMethod.GET)
    @UonePermissions
    public RestResponse getProjectSystemUrl(){
        return RestResponse.success().setData(projectUrl);
    }

}
