package cn.uone.crm.service.impl;

import cn.uone.bean.entity.crm.ExpenseOrderEntity;
import cn.uone.bean.entity.crm.ExpenseOrderSearchVo;
import cn.uone.crm.dao.TExpenseOrderDao;
import cn.uone.crm.service.IExpenseOrderService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Service
public class ExpenseOrderServiceImpl extends ServiceImpl<TExpenseOrderDao, ExpenseOrderEntity> implements IExpenseOrderService {

    @Override
    public BigDecimal getTotalAmount(String payerId) {
        return baseMapper.getTotalAmount(payerId);
    }

    @Override
    public IPage<ExpenseOrderEntity> selectPage(Page page, ExpenseOrderSearchVo searchVo) {
        return baseMapper.selectPage(page,searchVo);
    }
}
