package cn.uone.ams.tpi.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.uone.ams.tpi.api.baiwang.EinvoicebasicApi;
import cn.uone.ams.tpi.api.baiwang.OutputinvoiceApi;
import cn.uone.application.enumerate.ApiTypeEnum;
import cn.uone.bean.entity.tpi.baiwang.BaiwangConfigVo;
import cn.uone.bean.entity.tpi.baiwang.BaiwangInvoiceVo;
import cn.uone.bean.entity.tpi.baiwang.BaiwangRedInvoiceVo;
import cn.uone.cache.util.CacheUtil;
import cn.uone.fegin.crm.IExpenseConfigFegin;
import cn.uone.fegin.tpi.IBaiwangFegin;
import cn.uone.web.base.RestResponse;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/baiwang")
@Slf4j
public class BaiwangController implements IBaiwangFegin {

    @Autowired
    EinvoicebasicApi einvoicebasicApi;
    @Autowired
    OutputinvoiceApi outputinvoiceApi;
    @Autowired
    IExpenseConfigFegin expenseConfigFegin;

    /**
     * 查询对应税号下的数电账号当前的登录状态及登录方式
     * @param apiChannel 接口渠道
     * @param taxNo  税号
     * @param digitAccount  数电账号（电子税局实名手机号）
     * @return
     */
    @PostMapping("/getLoginResult")
    @ApiOperation("查询登录状态")
    public RestResponse getLoginResult(@RequestParam("apiChannel")String apiChannel,
                                @RequestParam("taxNo") String taxNo,
                                @RequestParam("digitAccount")String digitAccount) {

        BaiwangConfigVo config = getConfig(apiChannel);
        return einvoicebasicApi.getLoginResult(taxNo,digitAccount,config);
    }

    /**
     * 查询对应税号下的数电账号当前的开票实人认证（刷脸）状态及最近一次认证成功的时间
     * @param apiChannel 接口渠道
     * @param taxNo  税号
     * @param digitAccount  数电账号（电子税局实名手机号）
     * @return
     */
    @PostMapping("/getCertifyResult")
    @ApiOperation("查询实名认证状态")
    public RestResponse getCertifyResult(@RequestParam("apiChannel")String apiChannel,
                                       @RequestParam("taxNo") String taxNo,
                                       @RequestParam("digitAccount")String digitAccount) {

        BaiwangConfigVo config = getConfig(apiChannel);
        return einvoicebasicApi.getCertifyResult(taxNo,digitAccount,2,config);
    }

    /**
     * 获取接口参数配置
     * @param apiChannel
     * @return
     */
    private BaiwangConfigVo getConfig(String apiChannel){
        BaiwangConfigVo config = (BaiwangConfigVo) CacheUtil.get("BAIWANG_"+apiChannel);
        if(config == null){
            JSONObject json = expenseConfigFegin.getExpenseConfigByCode(apiChannel, ApiTypeEnum.BAIWANG.getValue());
            config = JSONUtil.toBean(json,BaiwangConfigVo.class);
            CacheUtil.putEx("BAIWANG_"+apiChannel,config,1l,TimeUnit.DAYS);
        }
        return config;
    }

    /**
     * 开具百旺发票
     * @param apiChannel 接口渠道
     * @param invoiceVo 开票信息
     * @return
     */
    @PostMapping("/baiwangInvoice")
    @ApiOperation("百旺开具发票")
    public RestResponse baiwangInvoice(@RequestParam("apiChannel") String apiChannel,@RequestBody BaiwangInvoiceVo invoiceVo) {
        BaiwangConfigVo config = getConfig(apiChannel);
        invoiceVo.setCallBackUrl(config.getCallBackUrl());
        RestResponse res = outputinvoiceApi.invoice(invoiceVo,config);
        if(!res.getSuccess()){
            CacheUtil.delete("BAIWANG_"+apiChannel);
        }
        return res;
    }

    /**
     * 交付发票
     * @param apiChannel
     * @param taxNo
     * @param digitInvoiceNo
     * @param pushPhone
     * @param pushEmail
     * @return
     */
    @PostMapping("/baiwangPush")
    @ApiOperation("百旺交付发票")
    public RestResponse baiwangPush(@RequestParam("apiChannel") String apiChannel,@RequestParam("taxNo") String taxNo,
                                       @RequestParam("digitInvoiceNo") String digitInvoiceNo,
                                       @RequestParam("pushPhone")String pushPhone,
                                       @RequestParam(value="pushEmail",required = false)String pushEmail) {
        BaiwangConfigVo config = getConfig(apiChannel);
        RestResponse res = outputinvoiceApi.push(taxNo, null, null,digitInvoiceNo,
                pushPhone,pushEmail, config);
        if(!res.getSuccess()){
            CacheUtil.delete("BAIWANG_"+apiChannel);
        }
        return res;
    }


    /**
     * 快捷红冲
     * @param apiChannel 接口渠道
     * @param redInvoiceVo 红票信息
     * @return
     */
    @PostMapping("/baiwangFastRed")
    @ApiOperation("快捷红冲")
    public RestResponse baiwangFastRed(@RequestParam("apiChannel") String apiChannel, @RequestBody BaiwangRedInvoiceVo redInvoiceVo) {
        BaiwangConfigVo config = getConfig(apiChannel);
        redInvoiceVo.setCallBackUrl(config.getCallBackUrl());
        RestResponse res = outputinvoiceApi.fastRed(redInvoiceVo,config);
        if(!res.getSuccess()){
            CacheUtil.delete("BAIWANG_"+apiChannel);
        }
        return res;
    }

    /**
     * 百旺发票重开
     * @param apiChannel
     * @param taxNo
     * @param orderNo
     * @return
     */
    @PostMapping("/baiwangRetry")
    @ApiOperation("百旺发票重开")
    public RestResponse baiwangRetry(@RequestParam("apiChannel") String apiChannel,@RequestParam("taxNo") String taxNo,
                                     @RequestParam("serialNo") String serialNo,@RequestParam("orderNo") String orderNo) {
        BaiwangConfigVo config = getConfig(apiChannel);
        return outputinvoiceApi.retry(taxNo,serialNo,orderNo,config);
    }

    /**
     * 查询百旺发票
     * @param apiChannel
     * @param taxNo
     * @param orderNo
     * @return
     */
    @PostMapping("/baiwangQuery")
    @ApiOperation("百旺查询开票结果")
    public RestResponse baiwangQuery(@RequestParam("apiChannel") String apiChannel,@RequestParam("taxNo") String taxNo,
                                     @RequestParam("orderNo") String orderNo) {
        BaiwangConfigVo config = getConfig(apiChannel);
        List<String> orderNos = Lists.newArrayList();
        orderNos.add(orderNo);
        return outputinvoiceApi.query(taxNo,null,orderNos,"0",config);
    }


}
