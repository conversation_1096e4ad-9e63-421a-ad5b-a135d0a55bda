# 付款单明细备注格式化需求文档

## 项目概述
在现有的付款申请系统中，需要为付款单明细添加格式化的备注信息。当前系统中的 `getOrderItemsWithOrderTypeFilter` 方法返回的 `allItems` 包含地址信息，需要将这些信息按照特定格式添加到付款单明细的备注中。

## 需求背景
- 当前系统：CosmicPayApplyServiceImpl 中的付款申请处理流程
- 数据来源：BilOrderItemWithOrderVo 对象中的 address 字段（通过 bilOrderVo.address 获取）
- 目标：生成格式化的备注信息用于付款单明细

## 功能需求

### 1. 备注格式规范
备注格式：`（退【滨海里2号及5号公寓**房间号**租户】【物业费】【2025-05-15】至【2025-05-31】）`

格式说明：
- `退` - 固定前缀，表示退款
- `【滨海里2号及5号公寓**房间号**租户】` - 地址信息，包含：
  - 项目名称：从 address 字段解析
  - 房间号：从 address 字段解析
  - 租户信息：从订单相关信息获取
- `【物业费】` - 费用类型，从 orderItemType 转换为中文名称
- `【2025-05-15】至【2025-05-31】` - 费用周期，从 startTime 和 endTime 获取

### 2. 数据字段映射
- **地址信息**：BilOrderItemWithOrderVo.bilOrderVo.address
- **费用类型**：BilOrderItemWithOrderVo.orderItemType（需要通过 OrderItemTypeEnum.getNameByValue() 转换）
- **开始时间**：BilOrderItemWithOrderVo.startTime
- **结束时间**：BilOrderItemWithOrderVo.endTime
- **租户信息**：BilOrderItemWithOrderVo.bilOrderVo.payer 或 singer

### 3. 技术实现要点
- 地址解析：需要从完整地址中提取项目名称和房间号
- 时间格式化：使用 yyyy-MM-dd 格式
- 费用类型转换：使用现有的 OrderItemTypeEnum 枚举
- 集成点：在 CosmicPayApplyServiceImpl 的付款申请处理流程中添加备注生成逻辑

## 技术约束
- 基于现有的 Spring Boot + MyBatis 架构
- 使用现有的枚举类进行类型转换
- 保持现有代码结构，最小化侵入性修改
- 确保备注信息的准确性和完整性

## 验收标准
1. 备注格式严格按照规范生成
2. 所有动态字段正确填充
3. 时间格式正确（yyyy-MM-dd）
4. 费用类型正确转换为中文名称
5. 地址信息正确解析和格式化
6. 集成到现有付款申请流程中无异常

## 风险评估
- 地址格式可能不统一，需要健壮的解析逻辑
- 时间字段可能为空，需要空值处理
- 费用类型可能存在未定义的值，需要默认处理

# 批量开票接口开发需求

## 项目背景
当前系统中已有单个账单开票的功能，但缺少批量开票功能。用户需要能够选择多个账单一次性进行开票处理，提高工作效率。

## 功能需求
1. 开发批量开票接口，支持一次处理多个账单的开票请求
2. 基于现有的makeInvoice方法进行扩展
3. 支持现有的两种开票类型：REPORT和BAIWANG
4. 提供完善的错误处理机制，确保批量处理过程中的稳定性
5. 完善的日志记录，记录每个账单的处理结果

## 技术要求
1. 在BilOrderController类中增加新的批量开票接口
2. 接口需支持传入多个订单ID进行处理
3. 保持与现有makeInvoice接口相同的安全和锁定机制
4. 提供适当的事务管理，确保数据一致性
5. 接口应当返回详细的处理结果，包括成功和失败的订单信息

## 期望输出
1. 新增批量开票接口
2. 完整的错误处理机制
3. 详细的日志记录
4. 接口文档说明

## 验收标准
1. 接口能够正确处理多个订单的开票请求
2. 在任一订单开票失败时不影响其他订单的处理
3. 返回明确的成功/失败信息，便于前端展示
4. 系统日志记录完整，便于问题追踪和分析
